import type { UniverAPI, ValidationRule, ValidationResult, FilterAPI } from './types/univer'
import { log } from './logger'

/**
 * 验证任务 - 简化的函数式接口
 */
export async function validateTask(univerAPI: UniverAPI, validationRule: ValidationRule): Promise<ValidationResult> {
  try {
    console.log('🔍 开始验证任务:', validationRule)
    log.debug('开始验证任务:', validationRule)

    const service = new ExcelValidationService(univerAPI)

    let result: ValidationResult
    switch (validationRule.type) {
      case 'cellValue':
      case 'input':
        result = await service.validateCellValue(validationRule)
        break
      case 'cellStyle':
        result = await service.validateCellStyle(validationRule)
        break
      case 'cellFormula':
        result = await service.validateCellFormula(validationRule)
        break
      case 'cellFormat':
        result = await service.validateCellFormat(validationRule)
        break
      case 'chart':
        result = await service.validateChart(validationRule)
        break
      case 'pivotTable':
        result = await service.validatePivotTable(validationRule)
        break
      case 'filter':
        result = await service.validateFilter(validationRule)
        break
      case 'sort':
        result = await service.validateSort(validationRule)
        break
      case 'multiSort':
        result = await service.validateMultiSort(validationRule)
        break
      case 'conditional_format':
      case 'conditionalFormat':
      case 'multiConditionalFormat':
        result = await service.validateConditionalFormat(validationRule)
        break
      case 'multiCellAlignment':
        result = await service.validateMultiCellAlignment(validationRule)
        break
      case 'multiBorder':
        result = await service.validateMultiBorder(validationRule)
        break
      case 'dataValidation':
        result = await service.validateDataValidation(validationRule)
        break
      case 'cellMerge':
        result = await service.validateCellMerge(validationRule)
        break
      case 'textWrap':
        result = await service.validateTextWrap(validationRule)
        break
      case 'formulaFill':
        result = await service.validateFormulaFill(validationRule)
        break
      default:
        result = {
          success: false,
          message: `不支持的验证类型: ${validationRule.type}`
        }
    }

    console.log('📊 验证结果:', result)
    return result
  } catch (error) {
    console.error('❌ 验证任务时发生错误:', error)
    log.error('验证任务时发生错误:', error)
    return {
      success: false,
      message: '验证过程中发生错误，请重试'
    }
  }
}

/**
 * Excel验证服务 - 基于官方Univer API
 */
export class ExcelValidationService {
  constructor(private univerAPI: UniverAPI) {}

  /**
   * 提取 Univer 单元格的实际值
   */
  private extractCellValue(cellValue: unknown): unknown {
    if (cellValue && typeof cellValue === 'object' && 'v' in cellValue) {
      return (cellValue as { v: unknown }).v;
    }
    return cellValue;
  }

  /**
   * 比较两个值是否相等
   */
  private compareValues(actual: unknown, expected: unknown): boolean {
    // 处理数字比较
    if (typeof expected === 'number' && typeof actual === 'number') {
      return Math.abs(actual - expected) < 0.0001 // 浮点数比较
    }

    // 处理字符串比较（忽略大小写和前后空格）
    if (typeof expected === 'string' && typeof actual === 'string') {
      return actual.trim().toLowerCase() === expected.trim().toLowerCase()
    }

    // 其他类型直接比较
    return actual === expected
  }

  /**
   * 验证单元格值
   */
  async validateCellValue(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || rule.expectedValue === undefined) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望值'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) throw new Error('未获取到工作簿');
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取活动工作表'
        }
      }

      const range = worksheet.getRange(rule.cell)
      if (!range) {
        return {
          success: false,
          message: `无法获取单元格 ${rule.cell}`
        }
      }

      const cellValue = range.getValue()
      const actualValue = this.extractCellValue(cellValue)

      // 类型转换和比较
      const expectedValue = rule.expectedValue
      const isMatch = this.compareValues(actualValue, expectedValue)

      return {
        success: isMatch,
        message: isMatch
          ? '单元格值验证通过！'
          : `单元格 ${rule.cell} 的值不正确。期望: "${expectedValue}"，实际: "${actualValue}"`,
        details: {
          cell: rule.cell,
          expected: expectedValue,
          actual: actualValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证单元格值时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格样式
   */
  async validateCellStyle(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedStyle) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望样式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }
      const range = worksheet.getRange(rule.cell)

      // 获取单元格数据和样式信息
      const cellData = range.getCellData()
      let style: any = {}

      // 根据Univer文档，样式可能是ID引用或直接的样式对象
      if (cellData?.s) {
        if (typeof cellData.s === 'string') {
          // 如果是字符串，说明是样式ID，需要从工作簿样式表中获取
          const workbook = this.univerAPI.getActiveWorkbook()
          let styles = {}

          try {
            // 使用save()方法获取工作簿数据，按照文档建议
            try {
              const workbookData = await (workbook as unknown as { save(): Promise<{ styles?: Record<string, any> }> }).save()
              styles = workbookData?.styles || {}
              log.debug('使用save()获取样式表:', { totalStyles: Object.keys(styles).length })
            } catch (saveError) {
              log.debug('save()方法失败，尝试其他方法:', saveError)

              // 备用方法：尝试从Univer实例获取
              const univerInstance = (this.univerAPI as unknown as { _univerInstance?: unknown })?._univerInstance
              if (univerInstance) {
                const currentWorkbook = (univerInstance as { getCurrentUniverSheetInstance(): unknown }).getCurrentUniverSheetInstance()
                const workbookSnapshot = (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.save?.() || (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.getSnapshot?.()
                styles = (workbookSnapshot as { styles?: Record<string, any> })?.styles || {}
              }
            }

            log.debug('获取到的样式表:', { totalStyles: Object.keys(styles).length, styleId: cellData.s })
          } catch (error) {
            log.debug('获取样式表失败:', error)
          }

          style = styles[cellData.s] || {}
          log.debug('从样式表获取样式:', { styleId: cellData.s, style, hasStyle: !!styles[cellData.s] })
        } else {
          // 如果是对象，直接使用
          style = cellData.s
          log.debug('直接使用样式对象:', style)
        }
      }

      log.debug('单元格数据和样式:', { cellData, style, styleType: typeof cellData?.s })

      const validationResults = []

      // 验证粗体
      if (rule.expectedStyle.bold !== undefined) {
        // 只检查标准的粗体属性，避免过于宽松的验证
        const isBold = style.bl === 1 || style.bl === true ||
                      style.bold === 1 || style.bold === true ||
                      style.fontWeight === 'bold' || style.fontWeight === 700 ||
                      (style.ft && (style.ft.bl === 1 || style.ft.bl === true))
        const expectedBold = rule.expectedStyle.bold

        log.debug('粗体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isBold: isBold,
          expectedBold: expectedBold,
          styleKeys: Object.keys(style),
          styleType: typeof style
        })

        validationResults.push({
          property: 'bold',
          expected: expectedBold,
          actual: isBold,
          match: isBold === expectedBold
        })
      }

      // 验证斜体
      if (rule.expectedStyle.italic !== undefined) {
        // 支持多种斜体属性格式
        const isItalic = style.it === 1 || style.it === true ||
                         style.italic === 1 || style.italic === true ||
                         style.fontStyle === 'italic' ||
                         (style.ft && (style.ft.it === 1 || style.ft.it === true))
        const expectedItalic = rule.expectedStyle.italic

        log.debug('斜体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isItalic: isItalic,
          expectedItalic: expectedItalic,
          styleKeys: Object.keys(style)
        })

        validationResults.push({
          property: 'italic',
          expected: expectedItalic,
          actual: isItalic,
          match: isItalic === expectedItalic
        })
      }

      // 验证字体系列
      if (rule.expectedStyle.fontFamily) {
        // 从样式对象获取字体系列 (ff属性)
        const actualFontFamily = style.ff || ''
        const expectedFontFamily = rule.expectedStyle.fontFamily

        log.debug('字体系列获取:', { actualFontFamily, expectedFontFamily, style })

        // 字体名称标准化
        const normalizeFont = (font: string) => {
          if (!font) return ''
          return font.toLowerCase()
            .replace(/['"]/g, '')
            .replace(/\s+/g, '')
            .trim()
        }

        const actualNormalized = normalizeFont(actualFontFamily)
        const expectedNormalized = normalizeFont(String(expectedFontFamily))

        // 字体匹配逻辑 - 严格匹配，只有实际设置了字体时才认为匹配
        let isMatch = false

        if (actualFontFamily && actualNormalized) {
          // 精确匹配
          if (actualNormalized === expectedNormalized) {
            isMatch = true
          }
          // 特殊处理宋体的各种表示方式 - 严格匹配，排除新宋体等其他字体
          else if (expectedNormalized === '宋体') {
            // 只有这些特定的字体名称才认为是宋体，明确排除nsimsun(新宋体)
            if (actualNormalized === 'nsimsun' || actualNormalized.includes('nsimsun')) {
              isMatch = false
            } else {
              isMatch = actualNormalized === 'simsun' ||
                       actualNormalized === '宋体' ||
                       actualNormalized === 'songti' ||
                       actualNormalized === 'st宋体' ||
                       actualNormalized === 'stsong'
            }
          }
          else if (actualNormalized === '宋体') {
            // 反向匹配
            isMatch = expectedNormalized === 'simsun' ||
                     expectedNormalized === '宋体' ||
                     expectedNormalized === 'songti' ||
                     expectedNormalized === 'st宋体' ||
                     expectedNormalized === 'stsong'
          }
          // 其他字体的精确匹配
          else {
            isMatch = actualNormalized === expectedNormalized
          }
        }

        log.debug('字体验证调试信息:', {
          cell: rule.cell,
          actualFontFamily,
          expectedFontFamily,
          actualNormalized,
          expectedNormalized,
          isMatch,
          hasActualFont: !!actualFontFamily,
          styleObject: style
        })

        validationResults.push({
          property: 'fontFamily',
          expected: expectedFontFamily,
          actual: actualFontFamily || '(未设置字体)',
          match: isMatch
        })
      }

      // 验证字体颜色
      if (rule.expectedStyle.color) {
        // 根据Univer文档，从样式对象获取字体颜色 (cl.rgb属性)
        const actualColor = style.cl?.rgb || ''

        const expectedColor = rule.expectedStyle.color

        console.log('🎨 字体颜色验证:', { actualColor, expectedColor, style })
        log.debug('字体颜色获取:', { actualColor, expectedColor, style })

        // 颜色值标准化 - 统一转换为小写并去除所有#前缀进行比较
        const normalizeColor = (color: string) => {
          if (!color) return ''
          return color.toLowerCase().replace(/#/g, '')  // 使用全局替换去除所有#号
        }

        const actualNormalized = normalizeColor(actualColor)
        const expectedNormalized = normalizeColor(String(expectedColor))

        const isMatch = actualNormalized === expectedNormalized

        log.debug('字体颜色验证调试信息:', {
          cell: rule.cell,
          actualColor,
          expectedColor,
          actualNormalized,
          expectedNormalized,
          isMatch,
          styleObject: style
        })

        console.log('🎨 字体颜色比较:', { actualNormalized, expectedNormalized, isMatch })

        validationResults.push({
          property: 'color',
          expected: expectedColor,
          actual: actualColor || '(未设置颜色)',
          match: isMatch
        })
      }

      // 验证背景色
      if (rule.expectedStyle.backgroundColor) {
        // 根据Univer文档，从样式对象获取背景颜色 (bg.rgb属性)
        const actualBgColor = style.bg?.rgb || ''

        const expectedBgColor = rule.expectedStyle.backgroundColor

        console.log('🎨 背景颜色验证:', { actualBgColor, expectedBgColor, style })
        log.debug('背景颜色获取:', { actualBgColor, expectedBgColor, style })

        // 颜色值标准化 - 统一转换为小写并去除所有#前缀进行比较
        const normalizeColor = (color: string) => {
          if (!color) return ''
          return color.toLowerCase().replace(/#/g, '')  // 使用全局替换去除所有#号
        }

        const actualNormalized = normalizeColor(actualBgColor)
        const expectedNormalized = normalizeColor(String(expectedBgColor))

        const isMatch = actualNormalized === expectedNormalized

        log.debug('背景颜色验证调试信息:', {
          cell: rule.cell,
          actualBgColor,
          expectedBgColor,
          actualNormalized,
          expectedNormalized,
          isMatch,
          styleObject: style
        })

        console.log('🎨 背景颜色比较:', { actualNormalized, expectedNormalized, isMatch })

        validationResults.push({
          property: 'backgroundColor',
          expected: expectedBgColor,
          actual: actualBgColor || '(未设置背景色)',
          match: isMatch
        })
      }

      // 检查是否有任何验证项目
      if (validationResults.length === 0) {
        return {
          success: false,
          message: `验证规则配置错误：没有指定要验证的样式属性`,
          details: { expectedStyle: rule.expectedStyle }
        }
      }

      // 检查所有验证结果
      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      if (allMatch) {
        return {
          success: true,
          message: '样式验证通过！',
          details: { validationResults }
        }
      } else {
        const failureMessages = failedResults.map(result =>
          `${result.property}: 期望 ${result.expected}, 实际 ${result.actual}`
        ).join('; ')

        return {
          success: false,
          message: `单元格 ${rule.cell} 的样式不正确。${failureMessages}`,
          details: { validationResults, failedResults }
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证样式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格公式
   */
  async validateCellFormula(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      const range = worksheet.getRange(rule.cell)
      const cellData = range.getCellData()

      // 获取公式
      const actualFormula = cellData.f || ''
      const expectedFormula = rule.expectedFormula || ''

      log.debug('公式验证:', {
        cell: rule.cell,
        actualFormula,
        expectedFormula,
        cellData
      })

      // 标准化公式比较（去除空格，统一大小写）
      const normalizeFormula = (formula: string) => {
        return formula.replace(/\s+/g, '').toUpperCase()
      }

      const normalizedActual = normalizeFormula(actualFormula)
      const normalizedExpected = normalizeFormula(expectedFormula)

      let formulaMatch = false
      if (expectedFormula) {
        formulaMatch = normalizedActual === normalizedExpected
      }

      // 验证计算结果
      let valueMatch = true
      if (rule.expectedValue !== undefined) {
        const actualValue = range.getValue()
        const extractedValue = this.extractCellValue(actualValue)
        valueMatch = this.compareValues(extractedValue, rule.expectedValue)
      }

      const success = (!expectedFormula || formulaMatch) && valueMatch

      if (success) {
        return {
          success: true,
          message: '公式验证通过！',
          details: {
            cell: rule.cell,
            formula: actualFormula,
            value: range.getValue()
          }
        }
      } else {
        let message = `单元格 ${rule.cell} 的公式验证失败。`
        if (expectedFormula && !formulaMatch) {
          message += `\n期望公式: ${expectedFormula}\n实际公式: ${actualFormula}`
        }
        if (rule.expectedValue !== undefined && !valueMatch) {
          message += `\n期望结果: ${rule.expectedValue}\n实际结果: ${range.getValue()}`
        }

        return {
          success: false,
          message,
          details: {
            cell: rule.cell,
            expectedFormula,
            actualFormula,
            expectedValue: rule.expectedValue,
            actualValue: range.getValue()
          }
        }
      }
    } catch (error) {
      log.error('验证公式时发生错误:', error)
      return {
        success: false,
        message: `验证公式时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证单元格格式
   */
  async validateCellFormat(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.cell) {
        return {
          success: false,
          message: '缺少单元格位置信息'
        }
      }

      const range = worksheet.getRange(rule.cell)
      const format = range.getNumberFormat()

      log.debug(`单元格 ${rule.cell} 的格式:`, format)

      if (rule.expectedFormat) {
        // 对于货币格式，需要更严格的验证
        if (rule.expectedFormat === 'currency') {
          // 检查是否为货币格式的各种可能表示
          const currencyFormats = [
            '¥#,##0.00',
            '¥#,##0',
            '$#,##0.00',
            '$#,##0',
            '￥#,##0.00',
            '￥#,##0',
            '"¥"#,##0.00',
            '"$"#,##0.00',
            '"￥"#,##0.00',
            // 更多可能的货币格式
            '[$¥-804]#,##0.00',
            '[$￥-804]#,##0.00',
            '[$¥-804]#,##0',
            '[$￥-804]#,##0'
          ]

          const isCurrencyFormat = currencyFormats.some(cf =>
            format === cf ||
            format.includes('¥') ||
            format.includes('$') ||
            format.includes('￥') ||
            format.includes('currency')
          )

          if (!isCurrencyFormat) {
            return {
              success: false,
              message: `单元格 ${rule.cell} 的格式不是货币格式。请按照操作步骤设置货币格式：\n1. 选择单元格 ${rule.cell}\n2. 切换到"数据"菜单\n3. 点击"货币"格式按钮\n\n当前格式: ${format}`
            }
          }
        } else {
          // 对于其他格式，进行精确匹配
          if (format !== rule.expectedFormat) {
            return {
              success: false,
              message: `单元格 ${rule.cell} 的格式不正确。期望: ${rule.expectedFormat}, 实际: ${format}`
            }
          }
        }
      }

      return {
        success: true,
        message: '单元格格式验证通过'
      }
    } catch (error) {
      log.error('验证单元格格式时发生错误:', error)
      return {
        success: false,
        message: '验证单元格格式时发生错误'
      }
    }
  }

  /**
   * 验证图表
   */
  async validateChart(rule: ValidationRule): Promise<ValidationResult> {
    try {
      // 检查DOM中是否存在图表相关元素
      const chartSelectors = [
        '.univer-chart',
        '.echarts-chart',
        '.chart-container',
        '[data-chart-id]',
        'canvas[data-zr-dom-id]', // ECharts canvas
        '.univer-drawing-object' // Univer绘图对象
      ]

      let chartFound = false
      for (const selector of chartSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          chartFound = true
          log.debug(`找到图表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      if (!chartFound) {
        return {
          success: false,
          message: '未找到图表。请按照操作步骤创建图表：\n1. 选择数据范围\n2. 点击"插入"选项卡\n3. 选择"图表"\n4. 选择合适的图表类型'
        }
      }

      return {
        success: true,
        message: '图表创建成功！任务完成。'
      }

    } catch (error) {
      log.error('图表验证错误:', error)
      return {
        success: false,
        message: `图表验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证数据透视表
   */
  async validatePivotTable(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      console.log('🔍 开始验证数据透视表:', rule)

      // 检查DOM中是否存在透视表相关元素
      const pivotSelectors = [
        '.univer-pivot-table',
        '.pivot-table',
        '[data-pivot-id]',
        '.univer-pivot'
      ]

      let pivotFound = false
      for (const selector of pivotSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          pivotFound = true
          console.log(`找到透视表元素: ${selector}, 数量: ${elements.length}`)
          log.debug(`找到透视表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      // 检查当前工作表中是否有透视表的特征数据结构
      let hasPivotStructure = false
      let pivotData = null
      let structureValidation = null

      try {
        const pivotRange = (rule as any).pivotRange || 'A1:Z50'
        const range = worksheet.getRange(pivotRange)
        const values = range.getValues()

        console.log('📊 检查透视表数据结构:', {
          pivotRange,
          values: values?.slice(0, 10), // 只显示前10行
          expectedStructure: rule.expectedStructure
        })

        if (values && values.length > 0) {
          pivotData = values

          // 如果有期望结构，必须严格验证结构
          if (rule.expectedStructure) {
            structureValidation = this.validatePivotStructure(values, rule.expectedStructure)

            // 只有结构验证通过才认为有透视表
            if (structureValidation.isValid) {
              hasPivotStructure = true
              console.log('透视表结构验证通过')
            } else {
              console.log('透视表结构验证失败:', structureValidation.message)
            }
          } else {
            // 如果没有期望结构，使用宽松的关键字检查（但仍需要多个条件）
            let keywordCount = 0
            const foundKeywords: string[] = []

            for (let i = 0; i < Math.min(values.length, 20); i++) {
              for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                const cellValue = values[i][j]
                if (cellValue && typeof cellValue === 'string') {
                  const pivotKeywords = ['总计', '小计', '求和', '计数', '平均值', '最大值', '最小值', 'Sum', 'Count', 'Average', 'Total']
                  const matchedKeyword = pivotKeywords.find(keyword => cellValue.includes(keyword))
                  if (matchedKeyword && !foundKeywords.includes(matchedKeyword)) {
                    foundKeywords.push(matchedKeyword)
                    keywordCount++
                  }
                }
              }
            }

            // 需要至少找到2个不同的透视表关键字才认为是透视表
            hasPivotStructure = keywordCount >= 2
            console.log(`找到透视表关键字: ${foundKeywords.join(', ')}, 数量: ${keywordCount}`)
          }
        }
      } catch (e) {
        console.error('检查透视表结构失败:', e)
        log.debug('检查透视表结构失败:', e)
      }

      const pivotCreated = pivotFound || hasPivotStructure

      if (!pivotCreated) {
        // 如果有期望结构但验证失败，返回具体的失败原因
        if (rule.expectedStructure && structureValidation && !structureValidation.isValid) {
          return {
            success: false,
            message: `数据透视表结构不正确。${structureValidation.message}`,
            details: {
              expectedStructure: rule.expectedStructure,
              actualData: pivotData?.slice(0, 10),
              structureValidation: structureValidation
            }
          }
        }

        return {
          success: false,
          message: '未检测到数据透视表。请按照操作步骤创建透视表：\n1. 选择数据范围\n2. 右键点击选择"数据透视表"或通过"插入"菜单\n3. 确认数据范围并选择放置位置\n4. 设置行字段、列字段和值字段\n5. 点击"确定"创建透视表',
          details: {
            expectedStructure: rule.expectedStructure,
            pivotRange: rule.pivotRange,
            structureValidation: structureValidation
          }
        }
      }

      return {
        success: true,
        message: '数据透视表验证成功！透视表结构和内容都正确。',
        details: {
          hasPivotStructure,
          pivotFound,
          expectedStructure: rule.expectedStructure,
          actualData: pivotData?.slice(0, 5), // 只返回前5行作为示例
          structureValidation: structureValidation
        }
      }

    } catch (error) {
      console.error('❌ 透视表验证错误:', error)
      log.error('透视表验证错误:', error)
      return {
        success: false,
        message: `透视表验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证透视表结构
   */
  private validatePivotStructure(data: any[][], expectedStructure: any): { isValid: boolean; message: string } {
    try {
      console.log('🏗️ 验证透视表结构:', { data: data.slice(0, 5), expectedStructure })

      if (!expectedStructure) {
        return { isValid: true, message: '无结构要求' }
      }

      const validationResults = []

      // 检查行标题 - 必须全部存在
      if (expectedStructure.rowHeaders && Array.isArray(expectedStructure.rowHeaders)) {
        const foundRowHeaders = []
        for (const expectedHeader of expectedStructure.rowHeaders) {
          let found = false
          for (let i = 0; i < Math.min(data.length, 15); i++) {
            for (let j = 0; j < Math.min(data[i].length, 15); j++) {
              if (data[i][j] === expectedHeader) {
                found = true
                foundRowHeaders.push(expectedHeader)
                break
              }
            }
            if (found) break
          }
          if (!found) {
            validationResults.push(`缺少行标题: ${expectedHeader}`)
          }
        }
        console.log('行标题验证:', { expected: expectedStructure.rowHeaders, found: foundRowHeaders })
      }

      // 检查列标题 - 必须全部存在
      if (expectedStructure.columnHeaders && Array.isArray(expectedStructure.columnHeaders)) {
        const foundColumnHeaders = []
        for (const expectedHeader of expectedStructure.columnHeaders) {
          let found = false
          for (let i = 0; i < Math.min(data.length, 15); i++) {
            for (let j = 0; j < Math.min(data[i].length, 15); j++) {
              if (data[i][j] === expectedHeader) {
                found = true
                foundColumnHeaders.push(expectedHeader)
                break
              }
            }
            if (found) break
          }
          if (!found) {
            validationResults.push(`缺少列标题: ${expectedHeader}`)
          }
        }
        console.log('列标题验证:', { expected: expectedStructure.columnHeaders, found: foundColumnHeaders })
      }

      // 检查期望的总计值
      if (expectedStructure.expectedTotalValue !== undefined) {
        let totalFound = false
        for (let i = 0; i < Math.min(data.length, 15); i++) {
          for (let j = 0; j < Math.min(data[i].length, 15); j++) {
            const cellValue = data[i][j]
            if (typeof cellValue === 'number' && cellValue === expectedStructure.expectedTotalValue) {
              totalFound = true
              break
            }
          }
          if (totalFound) break
        }
        if (!totalFound) {
          validationResults.push(`缺少期望的总计值: ${expectedStructure.expectedTotalValue}`)
        }
        console.log('总计值验证:', { expected: expectedStructure.expectedTotalValue, found: totalFound })
      }

      // 如果是严格验证模式，所有条件都必须满足
      if (expectedStructure.strictValidation && validationResults.length > 0) {
        return {
          isValid: false,
          message: `透视表结构验证失败: ${validationResults.join('; ')}`
        }
      }

      // 非严格模式下，至少要满足一半的条件
      const totalChecks = (expectedStructure.rowHeaders?.length || 0) +
                         (expectedStructure.columnHeaders?.length || 0) +
                         (expectedStructure.expectedTotalValue !== undefined ? 1 : 0)
      const failedChecks = validationResults.length
      const successRate = totalChecks > 0 ? (totalChecks - failedChecks) / totalChecks : 1

      if (successRate < 0.5) {
        return {
          isValid: false,
          message: `透视表结构验证失败: ${validationResults.join('; ')}`
        }
      }

      return { isValid: true, message: '结构验证通过' }

    } catch (error) {
      console.error('透视表结构验证错误:', error)
      return {
        isValid: false,
        message: `结构验证失败: ${error}`
      }
    }
  }

  /**
   * 验证筛选功能 - 基于官方FFilter API
   */
  async validateFilter(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.dataRange) {
      return {
        success: false,
        message: '验证规则配置错误：缺少数据范围'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      log.validation('筛选验证 - 开始基于官方FFilter API的验证')

      // 使用官方FFilter API进行验证
      const filter = worksheet.getFilter()
      log.validation('筛选验证 - 获取筛选器:', filter)

      // 尝试多种方式检测筛选器
      let filterDetected = false
      let filterAPI: any = null

      if (filter) {
        filterDetected = true
        filterAPI = filter
        log.validation('筛选验证 - 通过getFilter()检测到筛选器')
      } else {
        // 尝试其他方式检测筛选器
        try {
          const range = worksheet.getRange(rule.dataRange)
          const rangeFilter = (range as any).getFilter?.()
          if (rangeFilter) {
            filterDetected = true
            filterAPI = rangeFilter
            log.validation('筛选验证 - 通过range.getFilter()检测到筛选器')
          }
        } catch (error) {
          log.debug('筛选验证 - range.getFilter()调用失败:', error)
        }
      }

      if (!filterDetected) {
        // 尝试检查是否有筛选相关的数据结构
        try {
          const workbookData = (worksheet as any).getSnapshot?.() || (worksheet as any)._workbookData
          if (workbookData && workbookData.sheets) {
            const sheetData = Object.values(workbookData.sheets)[0] as any
            if (sheetData && sheetData.filterModel) {
              filterDetected = true
              log.validation('筛选验证 - 通过filterModel检测到筛选器')
            }
          }
        } catch (error) {
          log.debug('筛选验证 - filterModel检查失败:', error)
        }
      }

      if (!filterDetected) {
        return {
          success: false,
          message: '未检测到筛选器。请确保已正确执行以下步骤：\n\n1. 选择数据范围（包含表头）\n2. 启用筛选功能（数据 → 筛选）\n3. 设置筛选条件\n\n提示：筛选功能会在表头显示下拉箭头。',
          details: {
            filterDetected: false,
            hint: '请先启用筛选功能'
          }
        }
      }

      // 获取筛选范围
      // filterAPI已在上面声明和赋值
      const filterRange = filterAPI?.getRange?.()
      log.validation('筛选验证 - 筛选范围:', filterRange?.getA1Notation?.())

      // 获取被筛选掉的行
      const filteredOutRows = filterAPI?.getFilteredOutRows?.()
      log.validation('筛选验证 - 被筛选掉的行:', filteredOutRows)

      // 检查是否有筛选条件被设置
      let hasFilterCriteria = false
      const filterCriteriaDetails: Record<string, unknown> = {}

      if (filterRange && filterRange.getA1Notation) {
        const rangeNotation = filterRange.getA1Notation()
        const rangeMatch = rangeNotation?.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/)

        if (rangeMatch) {
          const startCol = rangeMatch[1]
          const endCol = rangeMatch[3]
          const startColIndex = this.getColumnIndex(startCol)
          const endColIndex = this.getColumnIndex(endCol)

          for (let colIndex = startColIndex; colIndex <= endColIndex; colIndex++) {
            try {
              const criteria = filterAPI?.getColumnFilterCriteria?.(colIndex)
              if (criteria) {
                hasFilterCriteria = true
                filterCriteriaDetails[`column_${colIndex}`] = criteria
                log.debug(`筛选验证 - 列${colIndex}的筛选条件:`, criteria)
              }
            } catch (error) {
              log.debug(`筛选验证 - 获取列${colIndex}筛选条件失败:`, error)
            }
          }
        }
      }

      // 验证筛选是否真正应用
      if (!hasFilterCriteria) {
        return {
          success: false,
          message: '未检测到筛选条件。请确保已正确设置筛选条件：\n\n1. 点击表头的下拉箭头\n2. 取消选中"全选"\n3. 只勾选需要显示的值\n4. 点击"确定"\n\n提示：设置筛选条件后，部分行会被隐藏。',
          details: {
            filterDetected: true,
            hasFilterCriteria: false,
            filterRange: filterRange?.getA1Notation?.(),
            hint: '筛选器已启用但未设置筛选条件'
          }
        }
      }

      // 验证筛选条件是否符合预期
      if (rule.expectedFilteredData && rule.expectedFilteredData.length > 0) {
        const criteriaValidation = await this.validateFilterCriteria(filterCriteriaDetails, rule)
        if (!criteriaValidation.isValid) {
          return {
            success: false,
            message: criteriaValidation.message || '筛选条件不符合预期',
            details: {
              filterDetected: true,
              hasFilterCriteria: true,
              filterCriteria: filterCriteriaDetails,
              criteriaValidation: criteriaValidation,
              hint: '筛选条件设置不正确'
            }
          }
        }
      }

      // 验证筛选结果
      if (filteredOutRows && filteredOutRows.length > 0) {
        log.validation('筛选验证 - 检测到筛选结果，有行被筛选掉')

        // 验证被筛选掉的行数是否合理
        if (rule.expectedVisibleRows !== undefined) {
          const range = worksheet.getRange(rule.dataRange)
          const values = range.getValues()
          const totalRows = values.length - 1 // 减去表头
          const expectedFilteredOutRows = totalRows - rule.expectedVisibleRows

          if (filteredOutRows.length !== expectedFilteredOutRows) {
            return {
              success: false,
              message: `筛选结果不正确。期望显示 ${rule.expectedVisibleRows} 行数据，但实际筛选掉了 ${filteredOutRows.length} 行（应该筛选掉 ${expectedFilteredOutRows} 行）。\n\n请检查筛选条件设置。`,
              details: {
                filterDetected: true,
                hasFilterCriteria: true,
                totalRows: totalRows,
                expectedVisibleRows: rule.expectedVisibleRows,
                expectedFilteredOutRows: expectedFilteredOutRows,
                actualFilteredOutRows: filteredOutRows.length,
                filteredOutRows: filteredOutRows,
                hint: '筛选结果行数不正确'
              }
            }
          }
        }
      } else {
        // 没有行被筛选掉，可能筛选条件包含了所有数据
        log.validation('筛选验证 - 没有行被筛选掉，检查筛选条件是否合理')

        if (rule.expectedVisibleRows !== undefined) {
          const range = worksheet.getRange(rule.dataRange)
          const values = range.getValues()
          const totalRows = values.length - 1 // 减去表头

          if (totalRows !== rule.expectedVisibleRows) {
            return {
              success: false,
              message: `筛选条件可能设置不正确。期望显示 ${rule.expectedVisibleRows} 行数据，但当前显示 ${totalRows} 行。\n\n请检查筛选条件是否正确设置。`,
              details: {
                filterDetected: true,
                hasFilterCriteria: true,
                totalRows: totalRows,
                expectedVisibleRows: rule.expectedVisibleRows,
                filteredOutRows: [],
                hint: '筛选条件可能包含了所有数据'
              }
            }
          }
        }
      }

      return {
        success: true,
        message: '筛选验证通过！筛选功能使用正确。',
        details: {
          filterDetected: true,
          hasFilterCriteria: true,
          filterRange: filterRange?.getA1Notation?.(),
          filterCriteria: filterCriteriaDetails,
          filteredOutRows: filteredOutRows || [],
          hint: '筛选功能使用正确'
        }
      }

    } catch (error) {
      console.error('❌ 筛选验证错误:', error)
      log.error('筛选验证错误:', error)
      return {
        success: false,
        message: `筛选验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证筛选条件是否符合预期
   */
  private async validateFilterCriteria(filterCriteria: unknown, rule: ValidationRule): Promise<{ isValid: boolean; message?: string }> {
    try {
      if (!rule.expectedFilteredData || rule.expectedFilteredData.length === 0) {
        return { isValid: true }
      }

      // 分析期望数据的筛选模式
      const expectedPatterns: Record<number, Set<unknown>> = {}

      for (const row of rule.expectedFilteredData) {
        if (row && typeof row === 'object') {
          for (const [colKey, value] of Object.entries(row)) {
            const colIndex = parseInt(colKey)
            if (!expectedPatterns[colIndex]) {
              expectedPatterns[colIndex] = new Set()
            }
            expectedPatterns[colIndex].add(value)
          }
        }
      }

      log.debug('筛选条件验证 - 期望的筛选模式:', expectedPatterns)
      log.debug('筛选条件验证 - 实际的筛选条件:', filterCriteria)

      // 检查筛选条件是否与期望模式匹配
      if (filterCriteria && typeof filterCriteria === 'object') {
        for (const [colKey, criteria] of Object.entries(filterCriteria)) {
          const colIndex = parseInt(colKey.split('_')[1])
          const expectedValues = expectedPatterns[colIndex]

          if (expectedValues && criteria && typeof criteria === 'object' &&
              'filters' in criteria && criteria.filters &&
              typeof criteria.filters === 'object' && 'filters' in criteria.filters) {
            const filtersObj = criteria.filters as { filters: unknown[] };
            const actualFilters = new Set(filtersObj.filters)
            const expectedFiltersArray = Array.from(expectedValues)

            // 检查筛选条件是否包含期望的值
            let hasExpectedValues = false
            for (const expectedValue of expectedFiltersArray) {
              if (actualFilters.has(String(expectedValue))) {
                hasExpectedValues = true
                break
              }
            }

            if (!hasExpectedValues) {
              return {
                isValid: false,
                message: `列${colIndex + 1}的筛选条件不正确。期望包含值: ${expectedFiltersArray.join(', ')}，实际筛选条件: ${Array.from(actualFilters).join(', ')}`
              }
            }
          }
        }
      }

      return { isValid: true }
    } catch (error) {
      log.debug('筛选条件验证失败:', error)
      return { isValid: true } // 验证失败时默认通过，避免误判
    }
  }

  /**
   * 验证排序
   */
  async validateSort(rule: ValidationRule): Promise<ValidationResult> {
    try {
      log.debug('开始验证单列排序:', rule)

      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) throw new Error('未获取到工作簿')
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) throw new Error('未获取到工作表')

      // 确定数据范围，如果没有指定则使用默认范围
      const dataRange = rule.dataRange || 'A1:C6'
      const range = worksheet.getRange(dataRange)
      const values = range.getValues()

      if (!values || values.length < 2) {
        return {
          success: false,
          message: '数据范围内没有足够的数据进行排序验证。请确保数据范围内有数据并且已经进行了排序操作。'
        }
      }

      log.debug('获取到的数据:', values)

      // 如果有期望的顺序，直接验证是否匹配（更可靠的方法）
      if (rule.expectedOrder && rule.expectedOrder.length > 0) {
        // 跳过表头，从第二行开始验证
        const dataRows = values.slice(1)
        const actualOrder = dataRows.map((row: unknown[]) => row[0]) // 第一列是标识列（姓名）

        log.debug('期望顺序:', rule.expectedOrder)
        log.debug('实际顺序:', actualOrder)

        const isOrderCorrect = this.checkExpectedOrder(actualOrder, rule.expectedOrder)

        if (!isOrderCorrect) {
          return {
            success: false,
            message: `排序结果与期望顺序不匹配。\n期望顺序: ${rule.expectedOrder.join(', ')}\n实际顺序: ${actualOrder.join(', ')}\n\n请按照以下步骤进行排序：\n1. 选择数据范围${dataRange}\n2. 点击"数据"选项卡\n3. 点击"排序"\n4. 选择按${rule.column || rule.sortColumn}列进行${rule.direction === 'desc' ? '降序' : '升序'}排序\n5. 点击"确定"`
          }
        }

        return {
          success: true,
          message: '排序验证通过！数据已按期望顺序排列。',
          details: {
            expectedOrder: rule.expectedOrder,
            actualOrder: actualOrder,
            sortColumn: rule.column || rule.sortColumn,
            direction: rule.direction || rule.sortDirection
          }
        }
      }

      // 如果没有期望顺序，则验证排序规则
      const sortColumn = rule.column || (rule as any).sortColumn
      if (!sortColumn) {
        return { success: false, message: '未指定排序列或期望顺序' }
      }

      const columnIndex = this.getColumnIndex(sortColumn)
      const direction = rule.direction || rule.sortDirection || 'asc'

      // 跳过表头，从第二行开始验证
      const dataRows = values.slice(1)

      // 验证数据是否按指定顺序排序
      const isCorrectlySorted = this.checkSortOrder(dataRows, columnIndex, direction)

      if (!isCorrectlySorted) {
        return {
          success: false,
          message: `数据未按${sortColumn}列${direction === 'asc' ? '升序' : '降序'}排序。请重新进行排序操作。`
        }
      }

      return {
        success: true,
        message: '排序验证通过！',
        details: {
          sortColumn: sortColumn,
          direction: direction
        }
      }
    } catch (error) {
      log.error('排序验证失败:', error)
      return { success: false, message: `排序验证过程中发生错误: ${error}` }
    }
  }

  /**
   * 验证多列排序
   */
  async validateMultiSort(rule: ValidationRule): Promise<ValidationResult> {
    try {
      log.debug('开始验证多列排序:', rule)

      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) throw new Error('未获取到工作簿')
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) throw new Error('未获取到工作表')

      // 确定数据范围
      const dataRange = rule.dataRange || 'A1:C7'
      const range = worksheet.getRange(dataRange)
      const values = range.getValues()

      if (!values || values.length < 2) {
        return {
          success: false,
          message: '数据范围内没有足够的数据进行排序验证。请确保数据范围内有数据并且已经进行了多列排序操作。'
        }
      }

      log.debug('获取到的数据:', values)

      if (!rule.sorts || rule.sorts.length === 0) {
        return { success: false, message: '未指定排序条件' }
      }

      // 如果有期望的顺序，直接验证是否匹配（更可靠的方法）
      if (rule.expectedOrder && rule.expectedOrder.length > 0) {
        // 跳过表头，从第二行开始验证
        const dataRows = values.slice(1)
        const actualOrder = dataRows.map((row: unknown[]) => row[0]) // 第一列是标识列（姓名）

        log.debug('期望顺序:', rule.expectedOrder)
        log.debug('实际顺序:', actualOrder)

        const isOrderCorrect = this.checkExpectedOrder(actualOrder, rule.expectedOrder)

        if (!isOrderCorrect) {
          const sortDesc = rule.sorts.map(s => `${s.column}列${s.direction === 'asc' ? '升序' : '降序'}`).join('，然后按')
          return {
            success: false,
            message: `多列排序结果与期望顺序不匹配。\n期望顺序: ${rule.expectedOrder.join(', ')}\n实际顺序: ${actualOrder.join(', ')}\n\n请按照以下步骤进行多列排序：\n1. 选择数据范围${dataRange}\n2. 点击"数据"选项卡\n3. 点击"排序"\n4. 设置主要排序条件：按${sortDesc}\n5. 点击"确定"`
          }
        }

        return {
          success: true,
          message: '多列排序验证通过！数据已按期望顺序排列。',
          details: {
            expectedOrder: rule.expectedOrder,
            actualOrder: actualOrder,
            sorts: rule.sorts
          }
        }
      }

      // 如果没有期望顺序，则验证排序规则
      // 跳过表头，从第二行开始验证
      const dataRows = values.slice(1)

      // 验证多列排序
      const isCorrectlySorted = this.checkMultiColumnSort(dataRows, rule.sorts)

      if (!isCorrectlySorted) {
        const sortDesc = rule.sorts.map(s => `${s.column}列${s.direction === 'asc' ? '升序' : '降序'}`).join('，然后按')
        return {
          success: false,
          message: `数据未按指定的多列排序规则排序。排序规则：按${sortDesc}。请重新进行多列排序操作。`
        }
      }

      return {
        success: true,
        message: '多列排序验证通过！',
        details: {
          sorts: rule.sorts
        }
      }
    } catch (error) {
      log.error('多列排序验证失败:', error)
      return { success: false, message: `多列排序验证过程中发生错误: ${error}` }
    }
  }

  /**
   * 验证条件格式
   */
  async validateConditionalFormat(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      // 暂时简化验证逻辑，避免API调用错误
      // 实际应用中需要根据Univer的具体API文档来实现
      log.debug('开始验证条件格式任务:', rule)

      // 验证简单条件格式
      if (rule.type === 'conditionalFormat') {
        return await this.validateSimpleConditionalFormat(rule, worksheet)
      }

      // 验证多条件格式
      if (rule.type === 'multiConditionalFormat') {
        return await this.validateMultiConditionalFormat(rule, worksheet)
      }

      return {
        success: false,
        message: `不支持的条件格式验证类型: ${rule.type}`
      }
    } catch (error) {
      log.error('条件格式验证失败:', error)
      return {
        success: false,
        message: '条件格式验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证简单条件格式
   */
  private async validateSimpleConditionalFormat(rule: ValidationRule, worksheet: any): Promise<ValidationResult> {
    const { range, condition, value, expectedFormattedCells, expectedBackgroundColor } = rule

    if (!range || !condition || !expectedFormattedCells || !expectedBackgroundColor) {
      return {
        success: false,
        message: '验证规则配置错误：缺少必要的条件格式参数'
      }
    }

    log.debug('验证简单条件格式:', { range, condition, value, expectedFormattedCells, expectedBackgroundColor })

    try {
      // 使用Univer API获取用户设置的条件格式规则
      const fWorksheet = worksheet

      // 获取指定范围的条件格式规则（只检查，不修改）
      const fRange = fWorksheet.getRange(range)
      let conditionalRules = null

      try {
        // 方法1：直接从range获取条件格式规则
        if (fRange && typeof fRange.getConditionalFormattingRules === 'function') {
          conditionalRules = fRange.getConditionalFormattingRules()
          log.debug('方法1 - 获取到的条件格式规则:', conditionalRules)
        }
      } catch (error) {
        log.debug('方法1获取条件格式规则失败:', error)
      }

      // 方法2：尝试从工作表获取所有条件格式规则
      if (!conditionalRules || conditionalRules.length === 0) {
        try {
          if (fWorksheet && typeof fWorksheet.getConditionalFormattingRules === 'function') {
            const allRules = fWorksheet.getConditionalFormattingRules()
            log.debug('方法2 - 工作表所有条件格式规则:', allRules)

            // 筛选出应用到指定范围的规则
            if (allRules && allRules.length > 0) {
              conditionalRules = allRules.filter((rule: any) => {
                try {
                  if (rule && typeof rule.getRanges === 'function') {
                    const ranges = rule.getRanges()
                    return ranges.some((r: any) => {
                      if (r && typeof r.getA1Notation === 'function') {
                        const notation = r.getA1Notation()
                        return notation === range || notation.includes(range)
                      }
                      return false
                    })
                  }
                  return false
                } catch (error) {
                  return false
                }
              })
              log.debug('方法2 - 筛选后的条件格式规则:', conditionalRules)
            }
          }
        } catch (error) {
          log.debug('方法2获取条件格式规则失败:', error)
        }
      }

      // 方法3：检查单元格数据中是否有条件格式信息
      if (!conditionalRules || conditionalRules.length === 0) {
        try {
          const cellsInRange = this.getCellsInRange(range)
          let hasConditionalFormat = false

          for (const cellAddr of cellsInRange) {
            const cellRange = fWorksheet.getRange(cellAddr)
            const cellData = cellRange.getCellData()

            // 检查单元格是否有条件格式相关的属性
            if (cellData && (cellData.conditionalFormat || cellData.cf)) {
              hasConditionalFormat = true
              conditionalRules = [cellData.conditionalFormat || cellData.cf]
              log.debug('方法3 - 从单元格数据获取到条件格式:', conditionalRules)
              break
            }
          }
        } catch (error) {
          log.debug('方法3获取条件格式规则失败:', error)
        }
      }

      // 如果没有检测到条件格式规则，说明用户还没有设置条件格式
      if (!conditionalRules || conditionalRules.length === 0) {
        log.debug('没有检测到条件格式规则，用户需要手动设置条件格式')

        // 不自动设置，只提示用户操作
        return {
          success: false,
          message: `请设置条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 点击"数据"菜单中的"条件格式"\n3. 选择"突出显示单元格规则" → "大于"\n4. 输入条件值：${value}\n5. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n6. 点击"确定"\n\n注意：验证的是背景色（#FF0000），不是文字色！`
        }
      }

      // 如果能获取到条件格式规则，进行规则验证
      if (conditionalRules && conditionalRules.length > 0) {
        log.debug('检测到条件格式规则，进行规则验证')

        // 验证条件格式规则是否正确
        const ruleValidation = this.validateConditionalFormattingRule(conditionalRules, {
          range,
          condition,
          value,
          expectedBackgroundColor
        })

        if (!ruleValidation.isValid) {
          return {
            success: false,
            message: `条件格式规则设置不正确：${ruleValidation.message}\n\n请确保：\n1. 条件类型为"大于"\n2. 条件值为 ${value}\n3. 背景色为红色（支持多种格式：${expectedBackgroundColor}、rgb(245,82,82)、#f05252等）\n4. 应用范围为 ${range}\n\n注意：验证的是背景色，不是文字色！颜色比较不区分大小写。`
          }
        }

        // 如果规则验证通过，返回成功
        log.debug('条件格式规则验证通过，任务完成')
        return {
          success: true,
          message: '条件格式验证成功！已正确设置条件格式规则。'
        }
      }

      // 这部分逻辑已经在上面处理过了，移除重复代码

      // 无论是否能获取到规则，都要验证单元格的实际格式效果
      log.debug('验证单元格实际格式效果')

      if (expectedFormattedCells && expectedFormattedCells.length > 0) {
        const formattedCellsFound = []
        const unformattedCells = []

        for (const cellAddress of expectedFormattedCells) {
          const hasCorrectFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedBackgroundColor)
          log.debug(`单元格 ${cellAddress} 背景色检查结果:`, hasCorrectFormat)

          if (hasCorrectFormat) {
            formattedCellsFound.push(cellAddress)
          } else {
            unformattedCells.push(cellAddress)
          }
        }

        // 检查是否所有期望的单元格都有正确的格式
        if (unformattedCells.length > 0) {
          return {
            success: false,
            message: `条件格式应用不完整。以下单元格缺少红色背景格式：${unformattedCells.join(', ')}\n\n请确保：\n1. 选择数据范围 ${range}\n2. 设置条件：大于 ${value}\n3. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n4. 点击"确定"应用格式\n\n注意：验证的是背景色（#FF0000），不是文字色！`
          }
        }

        // 检查是否有不应该格式化的单元格被格式化了
        const allCellsInRange = this.getCellsInRange(range)
        const unexpectedFormattedCells = []

        for (const cellAddress of allCellsInRange) {
          if (!expectedFormattedCells.includes(cellAddress)) {
            const hasFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedBackgroundColor)
            if (hasFormat) {
              unexpectedFormattedCells.push(cellAddress)
            }
          }
        }

        if (unexpectedFormattedCells.length > 0) {
          return {
            success: false,
            message: `条件格式应用过度。以下单元格不应该有红色背景格式：${unexpectedFormattedCells.join(', ')}\n\n请检查条件格式设置，确保只有满足条件的单元格才被格式化。`
          }
        }

        return {
          success: true,
          message: '条件格式验证成功！所有期望的单元格都正确应用了条件格式。'
        }
      }

      return {
        success: false,
        message: '条件格式验证失败：未指定期望的格式化单元格。'
      }

    } catch (error) {
      log.error('条件格式验证错误:', error)
      return {
        success: false,
        message: `条件格式验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证多条件格式
   */
  private async validateMultiConditionalFormat(rule: ValidationRule, worksheet: any): Promise<ValidationResult> {
    try {
      console.log('🎨 开始验证多条件格式:', rule)

      // 获取条件格式规则
      let conditionalRules = null
      try {
        const fRange = worksheet.getRange(rule.range)
        conditionalRules = fRange.getConditionalFormattingRules()
      } catch (error) {
        log.debug('获取条件格式规则失败:', error)
      }

      // 检查是否有足够的条件格式规则
      if (!conditionalRules || conditionalRules.length === 0) {
        return {
          success: false,
          message: '未检测到条件格式规则。请设置多个条件格式规则。',
          details: { expectedConditions: rule.conditions }
        }
      }

      // 验证每个期望的结果
      if (rule.expectedResults) {
        for (const [expectedColor, expectedCells] of Object.entries(rule.expectedResults)) {
          if (!Array.isArray(expectedCells)) continue

          for (const cellAddress of expectedCells) {
            const cellRange = worksheet.getRange(cellAddress)
            const cellData = cellRange.getCellData()
            const cellValue = cellData.v || 0

            console.log(`🎯 验证单元格 ${cellAddress}:`, {
              value: cellValue,
              expectedColor,
              style: cellData.s
            })

            // 检查单元格值是否符合条件
            let shouldHaveColor = false
            if (rule.conditions) {
              for (const condition of rule.conditions) {
                if (this.checkConditionMatch(cellValue, condition)) {
                  if (condition.color === expectedColor) {
                    shouldHaveColor = true
                    break
                  }
                }
              }
            }

            if (shouldHaveColor) {
              // 检查单元格是否有正确的背景颜色
              const actualBgColor = cellData.s?.bg || cellData.s?.backgroundColor || ''
              const normalizedActual = this.normalizeColor(actualBgColor)
              const normalizedExpected = this.normalizeColor(expectedColor)

              if (normalizedActual !== normalizedExpected) {
                return {
                  success: false,
                  message: `单元格 ${cellAddress} 的条件格式颜色不正确。期望: ${expectedColor}, 实际: ${actualBgColor}`,
                  details: {
                    cell: cellAddress,
                    value: cellValue,
                    expectedColor,
                    actualColor: actualBgColor
                  }
                }
              }
            }
          }
        }
      }

      return {
        success: true,
        message: '多条件格式验证通过！',
        details: {
          conditions: rule.conditions,
          expectedResults: rule.expectedResults,
          actualRules: conditionalRules
        }
      }

    } catch (error) {
      console.error('❌ 多条件格式验证错误:', error)
      return {
        success: false,
        message: `多条件格式验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 检查条件是否匹配
   */
  private checkConditionMatch(value: number, condition: any): boolean {
    const numValue = parseFloat(String(value))
    if (isNaN(numValue)) return false

    switch (condition.type) {
      case 'greaterThanOrEqual':
        return numValue >= condition.value
      case 'between':
        return numValue >= condition.minValue && numValue <= condition.maxValue
      case 'lessThan':
        return numValue < condition.value
      case 'greaterThan':
        return numValue > condition.value
      default:
        return false
    }
  }

  /**
   * 验证多单元格对齐
   */
  async validateMultiCellAlignment(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.cells || !Array.isArray(rule.cells)) {
        return {
          success: false,
          message: '验证规则配置错误：缺少单元格对齐配置'
        }
      }

      console.log('🔍 开始验证多单元格对齐:', rule)

      const validationResults = []

      for (const cellConfig of rule.cells) {
        const { cell, expectedAlignment } = cellConfig

        if (!cell || !expectedAlignment) {
          validationResults.push({
            cell: cell || '未知',
            expected: expectedAlignment || '未知',
            actual: '配置错误',
            match: false
          })
          continue
        }

        try {
          const range = worksheet.getRange(cell)
          if (!range) {
            validationResults.push({
              cell,
              expected: expectedAlignment,
              actual: '无法获取单元格',
              match: false
            })
            continue
          }

          // 获取单元格数据和样式信息
          const cellData = range.getCellData()
          let style: any = {}

          // 根据Univer文档，样式可能是ID引用或直接的样式对象
          if (cellData?.s) {
            if (typeof cellData.s === 'string') {
              // 如果是字符串，说明是样式ID，需要从工作簿样式表中获取
              const workbook = this.univerAPI.getActiveWorkbook()
              let styles = {}

              try {
                // 使用save()方法获取工作簿数据，按照文档建议
                try {
                  const workbookData = await (workbook as any).save()
                  styles = workbookData?.styles || {}
                  log.debug('使用save()获取样式表:', { totalStyles: Object.keys(styles).length })
                } catch (saveError) {
                  log.debug('save()方法失败，尝试其他方法:', saveError)

                  // 备用方法：尝试从Univer实例获取
                  const univerInstance = (this.univerAPI as any)?._univerInstance
                  if (univerInstance) {
                    const currentWorkbook = univerInstance.getCurrentUniverSheetInstance()
                    const workbookSnapshot = currentWorkbook?.save?.() || currentWorkbook?.getSnapshot?.()
                    styles = workbookSnapshot?.styles || {}
                  }
                }

                log.debug('获取到的样式表:', { totalStyles: Object.keys(styles).length, styleId: cellData.s })
              } catch (error) {
                log.debug('获取样式表失败:', error)
              }

              style = styles[cellData.s] || {}
              log.debug('从样式表获取样式:', { styleId: cellData.s, style, hasStyle: !!styles[cellData.s] })
            } else {
              // 如果是对象，直接使用
              style = cellData.s
              log.debug('直接使用样式对象:', style)
            }
          }

          // 从样式中获取水平对齐方式 (ht属性)
          const actualAlignment = style.ht

          log.debug('单元格对齐验证调试信息:', {
            cell,
            expectedAlignment,
            actualAlignment,
            actualAlignmentType: typeof actualAlignment,
            cellData,
            style
          })

          // 将Univer的对齐值转换为我们的标准值
          let normalizedActual = ''
          switch (actualAlignment) {
            case 1:
              normalizedActual = 'left'
              break
            case 2:
              normalizedActual = 'center'
              break
            case 3:
              normalizedActual = 'right'
              break
            default:
              // 如果没有设置对齐方式，默认为左对齐
              normalizedActual = 'left'
          }

          const isMatch = normalizedActual === expectedAlignment

          validationResults.push({
            cell,
            expected: expectedAlignment,
            actual: normalizedActual,
            match: isMatch
          })

        } catch (cellError) {
          log.error(`验证单元格 ${cell} 对齐方式时发生错误:`, cellError)
          validationResults.push({
            cell,
            expected: expectedAlignment,
            actual: '验证失败',
            match: false
          })
        }
      }

      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      let message = ''
      if (allMatch) {
        message = '单元格对齐方式验证通过！'
      } else {
        const failedDetails = failedResults.map(v =>
          `${v.cell}(期望:${v.expected}, 实际:${v.actual})`
        ).join('、')
        message = `单元格对齐方式不正确。失败的单元格: ${failedDetails}\n\n请按照以下步骤设置对齐方式：\n1. 选中需要设置的单元格\n2. 右键选择"设置单元格格式"\n3. 在"对齐"选项卡中选择相应的水平对齐方式\n4. 点击"确定"`
      }

      return {
        success: allMatch,
        message,
        details: {
          validationResults,
          totalCells: rule.cells.length,
          passedCells: validationResults.filter(r => r.match).length
        }
      }
    } catch (error) {
      console.error('❌ 验证多单元格对齐时发生错误:', error)
      return {
        success: false,
        message: `验证多单元格对齐时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证多边框
   */
  async validateMultiBorder(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.cells || !Array.isArray(rule.cells)) {
        return {
          success: false,
          message: '验证规则配置错误：缺少边框配置'
        }
      }

      console.log('🔍 开始验证多边框:', rule)

      const validationResults = []

      for (const cellConfig of rule.cells) {
        if (!cellConfig.range || !cellConfig.expectedBorder) {
          validationResults.push({
            range: cellConfig.range || '未知',
            expected: cellConfig.expectedBorder || '未知',
            actual: '配置错误',
            match: false
          })
          continue
        }

        // 使用改进的边框验证逻辑
        const borderResult = await this.validateRangeBorder(
          worksheet,
          cellConfig.range,
          cellConfig.expectedBorder,
          cellConfig.expectedBorderColor
        )

        validationResults.push({
          range: cellConfig.range,
          expected: cellConfig.expectedBorder,
          actual: borderResult.actualBorder,
          match: borderResult.isValid,
          description: cellConfig.description
        })
      }

      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      let message = ''
      if (allMatch) {
        message = '边框设置验证通过！'
      } else {
        const failedDetails = failedResults.map(v =>
          `${v.range}(期望:${v.expected}, 实际:${v.actual})`
        ).join('、')
        message = `边框设置不正确。失败的区域: ${failedDetails}\n\n请按照以下步骤设置边框：\n1. 选中需要设置的区域\n2. 右键选择"设置单元格格式"\n3. 在"边框"选项卡中选择相应的边框样式\n4. 如需设置边框颜色，请先选择颜色再设置边框\n5. 点击"确定"`
      }

      return {
        success: allMatch,
        message,
        details: {
          validationResults,
          totalRanges: rule.cells.length,
          passedRanges: validationResults.filter(r => r.match).length
        }
      }
    } catch (error) {
      console.error('❌ 验证多边框时发生错误:', error)
      return {
        success: false,
        message: `验证多边框时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证数据验证
   */
  async validateDataValidation(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      // 获取指定单元格的范围
      const range = worksheet.getRange(rule.cell)
      if (!range) {
        return {
          success: false,
          message: `无法获取单元格 ${rule.cell}`
        }
      }

      log.debug('开始严格验证数据验证规则:', rule)

      // 使用Univer Facade API进行严格验证
      let dataValidationRule = null
      let hasValidDataValidation = false

      try {
        // 方法1：直接从range获取数据验证规则
        if (range && typeof (range as any).getDataValidation === 'function') {
          try {
            dataValidationRule = (range as any).getDataValidation()
            log.debug('方法1 - 从range获取到的数据验证规则:', dataValidationRule)
          } catch (error) {
            log.debug('方法1失败:', error)
          }
        }

        // 方法2：尝试使用Facade API
        if (!dataValidationRule) {
          const fWorksheet = worksheet as any
          const fRange = fWorksheet.getRange ? fWorksheet.getRange(rule.cell) : range

          if (fRange && typeof fRange.getDataValidation === 'function') {
            try {
              dataValidationRule = fRange.getDataValidation()
              log.debug('方法2 - 从Facade API获取到的数据验证规则:', dataValidationRule)
            } catch (error) {
              log.debug('方法2失败:', error)
            }
          }
        }

        // 方法3：尝试获取工作表的所有数据验证规则
        if (!dataValidationRule) {
          const fWorksheet = worksheet as any
          if (fWorksheet.getDataValidations && typeof fWorksheet.getDataValidations === 'function') {
            try {
              const allValidations = fWorksheet.getDataValidations()
              log.debug('方法3 - 工作表所有数据验证规则:', allValidations)

              if (allValidations && allValidations.length > 0) {
                for (const validation of allValidations) {
                  if (validation && typeof validation.getRanges === 'function') {
                    const ranges = validation.getRanges()
                    const matchesCell = ranges.some((rangeItem: any) => {
                      if (!rangeItem || typeof rangeItem.getA1Notation !== 'function') return false
                      try {
                        const notationResult = rangeItem.getA1Notation()
                        const notation = String(notationResult || '')
                        return notation === rule.cell || (notation.length > 0 && rule.cell && notation.includes(rule.cell))
                      } catch (error) {
                        return false
                      }
                    })

                    if (matchesCell) {
                      dataValidationRule = validation
                      break
                    }
                  }
                }
              }
            } catch (error) {
              log.debug('方法3失败:', error)
            }
          }
        }

        // 方法4：检查单元格数据中是否有数据验证信息
        if (!dataValidationRule) {
          try {
            const cellData = range.getCellData()
            log.debug('方法4 - 检查单元格数据:', cellData)

            // 检查单元格是否有数据验证相关的属性
            if (cellData && ((cellData as any).dataValidation || (cellData as any).dv)) {
              dataValidationRule = (cellData as any).dataValidation || (cellData as any).dv
              log.debug('方法4 - 从单元格数据获取到验证规则:', dataValidationRule)
            }
          } catch (error) {
            log.debug('方法4失败:', error)
          }
        }

        // 如果获取到数据验证规则，进行详细验证
        if (dataValidationRule) {
          log.debug('检测到数据验证规则，开始详细验证...')

          // 获取验证规则的类型和值
          const validationRule = dataValidationRule as any
          let criteriaType = null
          let criteriaValues = []

          // 尝试多种方式获取验证规则信息
          try {
            if (typeof validationRule.getCriteriaType === 'function') {
              criteriaType = validationRule.getCriteriaType()
            } else if (validationRule.type) {
              criteriaType = validationRule.type
            } else if (validationRule.criteriaType) {
              criteriaType = validationRule.criteriaType
            }

            if (typeof validationRule.getCriteriaValues === 'function') {
              criteriaValues = validationRule.getCriteriaValues()
            } else if (validationRule.values) {
              criteriaValues = validationRule.values
            } else if (validationRule.criteriaValues) {
              criteriaValues = validationRule.criteriaValues
            }
          } catch (error) {
            log.debug('获取验证规则详情失败:', error)
          }

          log.debug('验证规则类型:', criteriaType)
          log.debug('验证规则值:', criteriaValues)

          // 验证是否为列表类型（下拉菜单）
          if (rule.validationType === 'list') {
            // 更宽松的列表类型检查
            const isListType = criteriaType === 'list' ||
                             criteriaType === 'LIST' ||
                             criteriaType === 'dropdown' ||
                             criteriaType === 'DROPDOWN' ||
                             String(criteriaType).toLowerCase().includes('list') ||
                             String(criteriaType).toLowerCase().includes('dropdown') ||
                             (criteriaValues && criteriaValues.length > 0) ||
                             // 如果有数据验证规则存在，就认为可能是列表类型
                             (dataValidationRule && Object.keys(dataValidationRule).length > 0)

            log.debug('列表类型检查结果:', isListType)

            if (!isListType) {
              return {
                success: false,
                message: `单元格 ${rule.cell} 的数据验证类型不正确。\n期望类型：下拉菜单（列表）\n实际类型：${criteriaType}\n\n请重新设置为下拉菜单类型的数据验证。`,
                details: {
                  cell: rule.cell,
                  expectedType: 'list',
                  actualType: criteriaType,
                  hasDataValidation: true,
                  dataValidationRule: dataValidationRule
                }
              }
            }

            // 验证数据源是否正确（更宽松的验证）
            if (rule.source) {
              let sourceFound = false
              let actualSource = ''

              // 尝试多种方式获取数据源
              if (criteriaValues && criteriaValues.length > 0) {
                const [operator, formula1, formula2] = criteriaValues
                actualSource = String(formula1 || formula2 || '')
                log.debug('从criteriaValues获取数据源:', { operator, formula1, formula2, expectedSource: rule.source })
              }

              // 如果从criteriaValues没有获取到，尝试其他属性
              if (!actualSource && validationRule.source) {
                actualSource = String(validationRule.source)
                log.debug('从source属性获取数据源:', actualSource)
              }

              if (!actualSource && validationRule.formula) {
                actualSource = String(validationRule.formula)
                log.debug('从formula属性获取数据源:', actualSource)
              }

              // 检查数据源是否匹配（更宽松的匹配）
              if (actualSource) {
                const sourceMatches = this.validateDataValidationSource(rule.source, actualSource)
                if (sourceMatches.isValid) {
                  sourceFound = true
                }
              }

              // 如果没有找到匹配的数据源，但有数据验证规则，给出警告但仍然通过
              if (!sourceFound) {
                log.debug('数据源验证失败，但检测到数据验证规则存在，可能是API差异导致')
                // 不返回失败，而是继续验证
              }
            }

            hasValidDataValidation = true
          }
        }

      } catch (error) {
        log.debug('检查数据验证API失败:', error)
      }

      // 严格验证：如果没有检测到有效的数据验证，返回失败
      if (!hasValidDataValidation) {
        return {
          success: false,
          message: `单元格 ${rule.cell} 未设置正确的数据验证规则。\n\n请按照以下步骤设置：\n1. 选中单元格 ${rule.cell}\n2. 点击"数据"菜单\n3. 在"数据工具"组中点击"数据验证"\n4. 点击"新建规则"\n5. 条件类型选择"下拉菜单"\n6. ${rule.source?.includes('$') ? '选择"引用数据"，输入引用区间：' + rule.source : '选择"自定义"，添加选项：' + rule.source}\n7. 点击"确定"\n\n完成后单元格应显示下拉箭头。`,
          details: {
            cell: rule.cell,
            expectedValidationType: rule.validationType,
            expectedSource: rule.source,
            hasDataValidation: !!dataValidationRule,
            hasValidDataValidation: false
          }
        }
      }

      return {
        success: true,
        message: '数据验证规则设置正确！已检测到正确的数据验证配置。',
        details: {
          cell: rule.cell,
          validationType: rule.validationType,
          source: rule.source,
          hasDataValidation: true,
          hasValidDataValidation: true,
          dataValidationRule: dataValidationRule
        }
      }

    } catch (error) {
      log.error('验证数据验证规则时发生错误:', error)
      return {
        success: false,
        message: `验证数据验证规则时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格合并
   */
  async validateCellMerge(rule: ValidationRule): Promise<ValidationResult> {
    log.debug('开始验证单元格合并:', rule)

    if (!rule.mergedRanges || rule.mergedRanges.length === 0) {
      return {
        success: false,
        message: '验证规则中未指定需要合并的范围'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      const validationResults = []

      // 验证每个指定的合并范围
      for (const mergedRange of rule.mergedRanges) {
        const { range, description } = mergedRange

        try {
          const rangeObj = worksheet.getRange(range)
          if (!rangeObj) {
            validationResults.push({
              range,
              description,
              isMerged: false,
              error: '无法获取范围对象'
            })
            continue
          }

          // 使用Univer FRange.isMerged() API检查是否合并
          let isMerged = false
          try {
            isMerged = rangeObj.isMerged()
          } catch (error) {
            // 如果isMerged方法不可用，尝试其他方法
            log.debug('isMerged方法不可用，尝试其他检查方法:', error)

            // 备用方法：检查范围内的单元格是否有合并信息
            try {
              const cellData = rangeObj.getCellData()
              isMerged = !!(cellData.rowSpan && cellData.rowSpan > 1) ||
                        !!(cellData.colSpan && cellData.colSpan > 1)
            } catch (fallbackError) {
              log.debug('备用检查方法也失败:', fallbackError)
              isMerged = false
            }
          }

          log.debug(`范围 ${range} 合并状态:`, isMerged)

          validationResults.push({
            range,
            description,
            isMerged,
            expected: true
          })

        } catch (error) {
          log.error(`检查范围 ${range} 合并状态失败:`, error)
          validationResults.push({
            range,
            description,
            isMerged: false,
            error: `检查失败: ${error}`
          })
        }
      }

      // 检查所有范围是否都已合并
      const allMerged = validationResults.every(result => result.isMerged === true)
      const failedRanges = validationResults.filter(result => !result.isMerged)

      if (allMerged) {
        return {
          success: true,
          message: '所有指定范围都已正确合并！',
          details: { validationResults }
        }
      } else {
        const failedMessages = failedRanges.map(result =>
          `${result.description} (${result.range})${result.error ? ': ' + result.error : ''}`
        ).join('\n')

        return {
          success: false,
          message: `以下范围未正确合并：\n${failedMessages}\n\n请按照操作步骤进行单元格合并操作。`,
          details: { validationResults }
        }
      }

    } catch (error) {
      console.error('❌ 验证单元格合并时发生错误:', error)
      return {
        success: false,
        message: `验证单元格合并时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证文本换行
   */
  async validateTextWrap(rule: ValidationRule): Promise<ValidationResult> {
    log.debug('开始验证文本换行:', rule)

    if (!rule.cells || rule.cells.length === 0) {
      return {
        success: false,
        message: '验证规则中未指定需要验证的单元格'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      const validationResults = []

      // 验证每个指定的单元格
      for (const cellConfig of rule.cells) {
        const { cell, wrapType, expectedText, description } = cellConfig

        if (!cell) {
          validationResults.push({
            cell: '未知',
            description: description || '配置错误',
            success: false,
            error: '单元格地址未指定'
          })
          continue
        }

        try {
          const rangeObj = worksheet.getRange(cell)
          if (!rangeObj) {
            validationResults.push({
              cell,
              description: description || `验证${cell}`,
              success: false,
              error: '无法获取范围对象'
            })
            continue
          }

          // 获取单元格数据和样式
          const cellData = rangeObj.getCellData()
          const cellValue = cellData.v || ''

          // 获取样式信息
          let style: any = {}
          if (cellData?.s) {
            if (typeof cellData.s === 'string') {
              // 如果是字符串，说明是样式ID，需要从工作簿样式表中获取
              const workbook = this.univerAPI.getActiveWorkbook()
              let styles = {}

              try {
                // 使用save()方法获取工作簿数据
                try {
                  const workbookData = await (workbook as any).save()
                  styles = workbookData?.styles || {}
                } catch (saveError) {
                  log.debug('save()方法失败，尝试其他方法:', saveError)
                  // 备用方法
                  const univerInstance = (this.univerAPI as any)?._univerInstance
                  if (univerInstance) {
                    const currentWorkbook = univerInstance.getCurrentUniverSheetInstance()
                    const workbookSnapshot = currentWorkbook?.save?.() || currentWorkbook?.getSnapshot?.()
                    styles = workbookSnapshot?.styles || {}
                  }
                }
              } catch (error) {
                log.debug('获取样式表失败:', error)
              }

              style = styles[cellData.s] || {}
            } else {
              style = cellData.s
            }
          }

          log.debug(`单元格 ${cell} 文本换行验证:`, {
            wrapType,
            expectedText,
            cellValue,
            style,
            description
          })

          let validationResult: any = {
            cell,
            description: description || `验证${cell}`,
            wrapType,
            expectedText,
            actualText: String(cellValue)
          }

          if (wrapType === 'auto') {
            // 验证自动换行设置
            // 使用Univer FRange.getWrap() API检查是否设置了自动换行
            let hasWrap = false
            try {
              if (rangeObj.getWrap && typeof rangeObj.getWrap === 'function') {
                hasWrap = rangeObj.getWrap()
              } else {
                // 备用方法：检查样式中的文本换行属性
                hasWrap = style.tb === 1 || style.tb === true
              }
            } catch (error) {
              log.debug(`获取单元格 ${cell} 换行状态失败:`, error)
              // 使用样式检查作为备用
              hasWrap = style.tb === 1 || style.tb === true
            }

            log.debug(`单元格 ${cell} 自动换行状态:`, hasWrap)

            validationResult.success = hasWrap
            validationResult.expected = '自动换行已启用'
            validationResult.actual = hasWrap ? '自动换行已启用' : '自动换行未启用'
            validationResult.hasWrap = hasWrap

          } else if (wrapType === 'manual') {
            // 验证强制换行（手动换行符）
            let actualText: string | null = null

            // 尝试多种方法获取单元格的实际文本内容
            try {
              // 方法1：使用getValue()
              const rawValue = rangeObj.getValue()
              log.debug(`单元格 ${cell} getValue() 结果:`, rawValue)

              if (rawValue !== null && rawValue !== undefined) {
                actualText = String(rawValue)
              }

              // 方法2：如果getValue()没有结果，尝试getDisplayValue()
              if (!actualText && rangeObj.getDisplayValue) {
                const displayValue = rangeObj.getDisplayValue()
                log.debug(`单元格 ${cell} getDisplayValue() 结果:`, displayValue)
                if (displayValue !== null && displayValue !== undefined) {
                  actualText = String(displayValue)
                }
              }

              // 方法3：如果还是没有结果，尝试从cellData获取
              if (!actualText) {
                const cellData = rangeObj.getCellData()
                log.debug(`单元格 ${cell} getCellData() 结果:`, cellData)

                if (cellData && cellData.v !== null && cellData.v !== undefined) {
                  actualText = String(cellData.v)
                } else if (cellData && (cellData as any).p && (cellData as any).p.body && (cellData as any).p.body.dataStream) {
                  // 尝试从富文本数据中获取
                  actualText = (cellData as any).p.body.dataStream
                }
              }

            } catch (error) {
              log.error(`获取单元格 ${cell} 内容失败:`, error)
            }

            log.debug(`单元格 ${cell} 最终实际文本:`, actualText)
            log.debug(`单元格 ${cell} 期望文本:`, expectedText)

            // 检查文本内容是否包含换行符并匹配期望内容
            if (!actualText) {
              validationResult.success = false
              validationResult.error = '无法获取单元格文本内容，请确保已输入文本'
            } else if (expectedText) {
              // 更灵活的验证：检查是否包含期望的文本部分和换行符
              const expectedParts = expectedText.split('\n')
              const hasAllParts = expectedParts.every(part => actualText!.includes(part))
              const hasLineBreak = actualText.includes('\n') || actualText.includes('\r\n') || actualText.includes('\r')

              log.debug(`期望的文本部分:`, expectedParts)
              log.debug(`是否包含所有部分:`, hasAllParts)
              log.debug(`是否包含换行符:`, hasLineBreak)

              if (!hasAllParts) {
                validationResult.success = false
                validationResult.error = `文本内容不完整。期望包含: ${expectedParts.join(' 和 ')}，实际: "${actualText}"`
              } else if (!hasLineBreak) {
                validationResult.success = false
                validationResult.error = `文本缺少换行符。请在相应位置插入换行符（Alt+Enter）`
              } else {
                validationResult.success = true
              }
            } else {
              // 如果没有指定期望文本，只检查是否有换行符
              const hasLineBreak = actualText.includes('\n') || actualText.includes('\r\n') || actualText.includes('\r')
              validationResult.success = hasLineBreak
              if (!hasLineBreak) {
                validationResult.error = '文本缺少换行符'
              }
            }

            validationResult.actualText = actualText
            validationResult.expected = expectedText ? `包含换行符的文本: "${expectedText}"` : '包含换行符的文本'
            validationResult.actual = actualText ? `文本内容: "${actualText}"` : '无文本内容'
          } else {
            validationResult.success = false
            validationResult.error = `不支持的换行类型: ${wrapType}`
          }

          validationResults.push(validationResult)

        } catch (error) {
          log.error(`验证单元格 ${cell} 文本换行失败:`, error)
          validationResults.push({
            cell,
            description: description || `验证${cell}`,
            success: false,
            error: `验证失败: ${error}`
          })
        }
      }

      // 检查所有验证是否都通过
      const allSuccess = validationResults.every(result => result.success === true)
      const failedResults = validationResults.filter(result => !result.success)

      if (allSuccess) {
        return {
          success: true,
          message: '所有文本换行设置都验证通过！',
          details: { validationResults }
        }
      } else {
        const failedMessages = failedResults.map(result =>
          `${result.description} (${result.cell})${result.error ? ': ' + result.error : ''}`
        ).join('\n')

        return {
          success: false,
          message: `以下单元格的文本换行设置不正确：\n${failedMessages}\n\n请按照操作步骤进行文本换行设置。`,
          details: { validationResults }
        }
      }

    } catch (error) {
      console.error('❌ 验证文本换行时发生错误:', error)
      return {
        success: false,
        message: `验证文本换行时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证公式填充
   */
  async validateFormulaFill(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.range || !rule.expectedFormulas) {
        return {
          success: false,
          message: '验证规则配置错误：缺少公式范围或期望公式配置'
        }
      }

      console.log('🔍 开始验证公式填充:', rule)

      const validationResults = []

      // 检查是否有期望的公式
      if (!rule.expectedFormulas || rule.expectedFormulas.length === 0) {
        return {
          success: false,
          message: '验证规则中未指定期望的公式'
        }
      }

      console.log('📊 公式填充验证:', {
        range: rule.range,
        expectedFormulas: rule.expectedFormulas
      })

      // 验证每个期望的公式
      for (const expectedFormula of rule.expectedFormulas) {
        const cellAddress = expectedFormula.cell
        const cellRange = worksheet.getRange(cellAddress)
        const cellData = cellRange.getCellData()

        // 尝试多种方式获取单元格的公式
        let actualFormula = ''
        let cellValue = ''

        try {
          // 方法1：从cellData.f获取公式
          if (cellData.f) {
            actualFormula = cellData.f
          }

          // 方法2：从cellData.formula获取公式
          if (!actualFormula && (cellData as any).formula) {
            actualFormula = (cellData as any).formula
          }

          // 方法3：尝试使用range.getFormula()方法
          if (!actualFormula && typeof (cellRange as any).getFormula === 'function') {
            try {
              actualFormula = (cellRange as any).getFormula()
            } catch (error) {
              log.debug(`使用getFormula()方法失败:`, error)
            }
          }

          // 方法4：尝试使用range.getFormulaR1C1()方法
          if (!actualFormula && typeof (cellRange as any).getFormulaR1C1 === 'function') {
            try {
              actualFormula = (cellRange as any).getFormulaR1C1()
            } catch (error) {
              log.debug(`使用getFormulaR1C1()方法失败:`, error)
            }
          }

          // 获取单元格的值
          cellValue = cellData.v || (cellData as any).value || ''

          // 如果还是没有公式，尝试从值中推断
          if (!actualFormula && cellValue && typeof cellValue === 'number') {
            // 检查是否是计算结果，如果是，可能有公式但API没有返回
            log.debug(`单元格 ${cellAddress} 有数值但无公式，可能是计算结果`)
          }

        } catch (error) {
          log.error(`获取单元格 ${cellAddress} 公式失败:`, error)
        }

        console.log(`🧮 单元格 ${cellAddress} 公式验证:`, {
          expectedFormula: expectedFormula.formula,
          actualFormula,
          cellValue,
          cellData
        })

        // 标准化公式比较（移除空格，统一大小写）
        const normalizedExpected = String(expectedFormula.formula).replace(/\s+/g, '').toUpperCase()
        const normalizedActual = String(actualFormula).replace(/\s+/g, '').toUpperCase()

        // 更宽松的匹配逻辑
        let isMatch = false

        if (normalizedActual && normalizedExpected) {
          // 精确匹配
          isMatch = normalizedActual === normalizedExpected

          // 如果不匹配，尝试部分匹配
          if (!isMatch) {
            isMatch = normalizedActual.includes(normalizedExpected) && normalizedExpected.length > 0
          }

          // 如果还不匹配，尝试去掉等号的匹配
          if (!isMatch) {
            const expectedWithoutEquals = normalizedExpected.replace(/^=/, '')
            const actualWithoutEquals = normalizedActual.replace(/^=/, '')
            isMatch = actualWithoutEquals === expectedWithoutEquals
          }
        }

        // 如果没有公式但有期望的计算结果，检查值是否正确
        if (!isMatch && !actualFormula && cellValue) {
          // 尝试计算期望公式的结果并与实际值比较
          try {
            const expectedResult = this.calculateExpectedFormulaResult(expectedFormula.formula, cellAddress)
            if (expectedResult !== null && Math.abs(Number(cellValue) - expectedResult) < 0.001) {
              log.debug(`单元格 ${cellAddress} 值匹配期望结果，可能公式已正确设置但API未返回`)
              // 暂时不设置为匹配，因为我们需要验证公式本身
            }
          } catch (error) {
            log.debug(`计算期望结果失败:`, error)
          }
        }

        validationResults.push({
          cell: cellAddress,
          expected: expectedFormula.formula,
          actual: actualFormula || '(无公式)',
          value: cellValue,
          match: isMatch
        })
      }

      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      if (allMatch) {
        return {
          success: true,
          message: '公式填充验证通过！所有公式都正确填充。',
          details: { validationResults }
        }
      } else {
        const failureMessages = failedResults.map(result =>
          `${result.cell}: 期望 ${result.expected}, 实际 ${result.actual}`
        ).join('; ')

        return {
          success: false,
          message: `公式填充不正确。${failureMessages}`,
          details: { validationResults, failedResults }
        }
      }

    } catch (error) {
      console.error('❌ 验证公式填充时发生错误:', error)
      return {
        success: false,
        message: `验证公式填充时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 分析边框样式
   */
  private analyzeBorderStyle(borderData: any, expectedType: string): { type: string } {
    if (!borderData) {
      return { type: 'none' }
    }

    console.log('🔍 分析边框样式:', { borderData, expectedType })

    // 检查边框的各个方向
    const hasTop = borderData.t || borderData.top
    const hasBottom = borderData.b || borderData.bottom
    const hasLeft = borderData.l || borderData.left
    const hasRight = borderData.r || borderData.right

    const borderCount = [hasTop, hasBottom, hasLeft, hasRight].filter(Boolean).length

    // 根据边框数量和期望类型判断
    if (expectedType === 'outline') {
      // 外边框：至少要有上下左右边框
      return { type: borderCount >= 4 ? 'outline' : 'partial' }
    } else if (expectedType === 'all') {
      // 所有边框：检查是否有内部边框标识
      return { type: borderCount >= 4 ? 'all' : 'partial' }
    } else if (expectedType === 'thick') {
      // 粗边框：检查边框粗细
      const isThick = this.checkBorderThickness(borderData)
      return { type: isThick && borderCount >= 4 ? 'thick' : 'thin' }
    }

    return { type: borderCount > 0 ? 'unknown' : 'none' }
  }

  /**
   * 检查边框粗细
   */
  private checkBorderThickness(borderData: any): boolean {
    const checkSide = (side: any) => {
      if (!side) return false
      // 检查边框样式是否为粗边框
      return side.s === 'thick' || side.style === 'thick' || side.width > 1
    }

    return checkSide(borderData.t) || checkSide(borderData.b) ||
           checkSide(borderData.l) || checkSide(borderData.r)
  }

  /**
   * 检查边框颜色
   */
  private checkBorderColor(borderData: any, expectedColor: string): boolean {
    if (!borderData || !expectedColor) return true

    const normalizeColor = (color: any): string => {
      if (!color) return ''
      if (typeof color === 'string' && color.startsWith('#')) {
        return color.toLowerCase()
      }
      if (typeof color === 'object' && color.rgb) {
        return color.rgb.toLowerCase()
      }
      return String(color).toLowerCase()
    }

    const expectedNormalized = normalizeColor(expectedColor)

    // 检查各个边的颜色
    const sides = [borderData.t, borderData.b, borderData.l, borderData.r]
    return sides.some(side => {
      if (!side) return false
      const actualColor = normalizeColor(side.cl || side.color)
      return actualColor === expectedNormalized
    })
  }

  /**
   * 检查期望顺序
   */
  private checkExpectedOrder(actualOrder: unknown[], expectedOrder: unknown[]): boolean {
    if (actualOrder.length !== expectedOrder.length) {
      return false
    }

    return actualOrder.every((value, index) => value === expectedOrder[index])
  }

  /**
   * 获取列索引
   */
  private getColumnIndex(columnLetter: string): number {
    let result = 0
    for (let i = 0; i < columnLetter.length; i++) {
      result = result * 26 + (columnLetter.charCodeAt(i) - 'A'.charCodeAt(0) + 1)
    }
    return result - 1 // 转换为0基索引
  }

  /**
   * 检查排序顺序
   */
  private checkSortOrder(dataRows: unknown[][], columnIndex: number, direction: string): boolean {
    if (dataRows.length < 2) return true

    for (let i = 1; i < dataRows.length; i++) {
      const current = dataRows[i][columnIndex]
      const previous = dataRows[i - 1][columnIndex]

      if (direction === 'asc') {
        if (String(current) < String(previous)) return false
      } else {
        if (String(current) > String(previous)) return false
      }
    }

    return true
  }

  /**
   * 检查多列排序
   */
  private checkMultiColumnSort(dataRows: unknown[][], sorts: any[]): boolean {
    if (dataRows.length < 2) return true

    for (let i = 1; i < dataRows.length; i++) {
      const current = dataRows[i]
      const previous = dataRows[i - 1]

      for (const sort of sorts) {
        const columnIndex = this.getColumnIndex(sort.column)
        const currentValue = current[columnIndex]
        const previousValue = previous[columnIndex]

        if (currentValue !== previousValue) {
          if (sort.direction === 'asc') {
            if (String(currentValue) < String(previousValue)) return false
          } else {
            if (String(currentValue) > String(previousValue)) return false
          }
          break // 如果这一列已经决定了顺序，就不需要检查后续列
        }
      }
    }

    return true
  }

  /**
   * 验证数据验证的数据源是否正确
   */
  private validateDataValidationSource(expectedSource: string, actualSource: string): { isValid: boolean; message: string } {
    if (!actualSource) {
      return {
        isValid: false,
        message: '未检测到数据验证的数据源配置'
      }
    }

    log.debug('验证数据源匹配:', { expectedSource, actualSource })

    // 处理引用数据的情况（如 $D$1:$D$4）
    if (expectedSource.includes('$')) {
      // 标准化引用格式
      const normalizedExpected = expectedSource.replace(/\$/g, '').toUpperCase()
      const normalizedActual = actualSource.replace(/\$/g, '').toUpperCase()

      if (normalizedExpected === normalizedActual) {
        return { isValid: true, message: '数据源引用匹配' }
      }

      return {
        isValid: false,
        message: `数据源引用不匹配。期望: ${expectedSource}, 实际: ${actualSource}`
      }
    }

    // 处理自定义选项的情况（如 "选项1,选项2,选项3"）
    if (expectedSource.includes(',')) {
      const expectedOptions = expectedSource.split(',').map(opt => opt.trim()).sort()

      // 尝试解析实际的选项列表
      let actualOptions: string[] = []

      if (actualSource.includes(',')) {
        actualOptions = actualSource.split(',').map(opt => opt.trim()).sort()
      } else if (actualSource.includes(';')) {
        actualOptions = actualSource.split(';').map(opt => opt.trim()).sort()
      } else {
        // 可能是单个选项或其他格式
        actualOptions = [actualSource.trim()]
      }

      // 比较选项列表
      const optionsMatch = expectedOptions.length === actualOptions.length &&
                          expectedOptions.every((opt, index) => opt === actualOptions[index])

      if (optionsMatch) {
        return { isValid: true, message: '数据源选项匹配' }
      }

      return {
        isValid: false,
        message: `数据源选项不匹配。期望: [${expectedOptions.join(', ')}], 实际: [${actualOptions.join(', ')}]`
      }
    }

    // 简单的字符串匹配
    if (expectedSource.trim() === actualSource.trim()) {
      return { isValid: true, message: '数据源匹配' }
    }

    return {
      isValid: false,
      message: `数据源不匹配。期望: "${expectedSource}", 实际: "${actualSource}"`
    }
  }

  /**
   * 验证条件格式规则是否符合要求
   */
  private validateConditionalFormattingRule(rules: unknown[], expected: {
    range: string
    condition: string
    value: unknown
    expectedBackgroundColor: string
  }): { isValid: boolean; message: string } {
    try {
      log.debug('验证条件格式规则:', { rules, expected })

      // 检查是否有符合条件的规则
      for (const rule of rules) {
        log.debug('检查规则:', rule)

        // 检查规则类型
        if (this.checkRuleCondition(rule, expected.condition, expected.value)) {
          // 检查背景色
          if (this.checkRuleBackgroundColor(rule, expected.expectedBackgroundColor)) {
            // 检查应用范围
            if (this.checkRuleRange(rule, expected.range)) {
              return { isValid: true, message: '条件格式规则验证通过' }
            } else {
              return { isValid: false, message: `应用范围不正确，期望：${expected.range}` }
            }
          } else {
            return { isValid: false, message: `背景色不正确，期望：${expected.expectedBackgroundColor}` }
          }
        }
      }

      // 如果没有找到完全匹配的规则，尝试简化的验证逻辑
      for (const rule of rules) {
        const ruleObj = rule as any

        // 检查条件类型
        const ruleCondition = ruleObj.condition || ruleObj.type
        if (expected.condition === 'greaterThan' &&
            (ruleCondition === 'greaterThan' || ruleCondition === 'cellValue')) {

          // 检查条件值
          const ruleValue = ruleObj.value || ruleObj.formula1
          if (ruleValue !== undefined && String(ruleValue) === String(expected.value)) {
            return {
              isValid: true,
              message: '条件格式规则验证通过'
            }
          }
        }
      }

      return { isValid: false, message: `未找到匹配的条件格式规则，期望条件：${expected.condition}，值：${expected.value}` }
    } catch (error) {
      log.error('验证条件格式规则失败:', error)
      return { isValid: false, message: '验证条件格式规则时发生错误' }
    }
  }

  /**
   * 检查单元格背景色
   */
  private async checkCellBackgroundColor(worksheet: any, cellAddress: string, expectedColor: string): Promise<boolean> {
    try {
      const range = worksheet.getRange(cellAddress)
      const cellData = range.getCellData()

      log.debug(`检查单元格 ${cellAddress} 背景色:`, {
        cellData,
        expectedColor,
        style: cellData.s
      })

      // 尝试多种方式获取背景色
      let actualBgColor = ''

      // 方法1：从样式对象的bg属性获取
      if (cellData.s?.bg?.rgb) {
        actualBgColor = cellData.s.bg.rgb
      } else if (cellData.s?.bg) {
        actualBgColor = String(cellData.s.bg)
      }

      // 方法2：从样式对象的backgroundColor属性获取
      if (!actualBgColor && cellData.s?.backgroundColor) {
        actualBgColor = cellData.s.backgroundColor
      }

      // 方法3：从样式对象的fill属性获取
      if (!actualBgColor && cellData.s?.fill?.fgColor?.rgb) {
        actualBgColor = cellData.s.fill.fgColor.rgb
      }

      // 方法4：检查是否有样式ID，从工作簿样式表获取
      if (!actualBgColor && typeof cellData.s === 'string') {
        try {
          const workbook = this.univerAPI.getActiveWorkbook()
          const workbookData = await (workbook as any).save()
          const styles = workbookData?.styles || {}
          const style = styles[cellData.s]

          if (style?.bg?.rgb) {
            actualBgColor = style.bg.rgb
          } else if (style?.backgroundColor) {
            actualBgColor = style.backgroundColor
          }
        } catch (error) {
          log.debug('从样式表获取背景色失败:', error)
        }
      }

      log.debug(`单元格 ${cellAddress} 实际背景色:`, actualBgColor)

      // 颜色标准化比较
      const normalizeColor = (color: string) => {
        if (!color) return ''
        let normalized = color.toLowerCase().replace(/#/g, '')

        // 处理常见的红色格式
        if (normalized === 'f05252' || normalized === 'ff0000' || normalized === 'red') {
          return 'f05252'
        }

        return normalized
      }

      const actualNormalized = normalizeColor(actualBgColor)
      const expectedNormalized = normalizeColor(expectedColor)

      log.debug(`颜色比较:`, {
        actual: actualNormalized,
        expected: expectedNormalized,
        match: actualNormalized === expectedNormalized
      })

      return actualNormalized === expectedNormalized
    } catch (error) {
      log.debug(`检查单元格 ${cellAddress} 背景色失败:`, error)
      return false
    }
  }



  /**
   * 将列索引转换为列字母
   */
  private getColumnLetter(index: number): string {
    let result = ''
    let num = index + 1

    while (num > 0) {
      num--
      result = String.fromCharCode(65 + (num % 26)) + result
      num = Math.floor(num / 26)
    }

    return result
  }



  /**
   * 标准化颜色值
   */
  private normalizeColor(color: any): string {
    if (!color) return ''

    if (typeof color === 'string') {
      return color.toLowerCase().replace(/#/g, '')
    }

    if (typeof color === 'object' && color.rgb) {
      return color.rgb.toLowerCase().replace(/#/g, '')
    }

    return String(color).toLowerCase().replace(/#/g, '')
  }

  /**
   * 计算期望公式的结果（简单计算）
   */
  private calculateExpectedFormulaResult(formula: string, cellAddress: string): number | null {
    try {
      // 简单的公式计算，仅支持基本的乘法运算
      const cleanFormula = formula.replace(/^=/, '').toUpperCase()

      // 匹配 A2*B2 这样的模式
      const multiplyMatch = cleanFormula.match(/([A-Z]+\d+)\*([A-Z]+\d+)/)
      if (multiplyMatch) {
        const [, cell1, cell2] = multiplyMatch

        // 获取工作表
        const workbook = this.univerAPI.getActiveWorkbook()
        const worksheet = workbook?.getActiveSheet()

        if (worksheet) {
          const value1 = worksheet.getRange(cell1).getCellData().v || 0
          const value2 = worksheet.getRange(cell2).getCellData().v || 0

          return Number(value1) * Number(value2)
        }
      }

      return null
    } catch (error) {
      return null
    }
  }

  /**
   * 检查规则条件是否匹配
   */
  private checkRuleCondition(rule: unknown, expectedCondition: string, expectedValue: unknown): boolean {
    try {
      log.debug('检查规则条件:', { rule, expectedCondition, expectedValue })

      // 根据实际的Univer API结构验证条件格式规则
      const ruleObj = rule as { rule?: unknown }
      if (rule && ruleObj.rule) {
        const cfRule = ruleObj.rule as { type?: string; value?: unknown; operator?: string }

        // 检查条件类型
        if (expectedCondition === 'greaterThan') {
          if (cfRule.operator === 'greaterThan' && cfRule.type === 'highlightCell') {
            // 检查条件值
            if (cfRule.value === expectedValue) {
              log.debug('条件格式规则条件验证通过')
              return true
            } else {
              log.debug(`条件值不匹配，期望：${expectedValue}，实际：${cfRule.value}`)
            }
          } else {
            log.debug(`条件类型不匹配，期望：greaterThan，实际：${cfRule.operator}`)
          }
        }
      } else {
        log.debug('规则结构不正确')
      }

      return false
    } catch (error) {
      log.error('检查规则条件失败:', error)
      return false
    }
  }

  /**
   * 检查规则背景色是否匹配
   */
  private checkRuleBackgroundColor(rule: unknown, expectedColor: string): boolean {
    try {
      log.debug('检查规则背景色:', { rule, expectedColor })

      // 根据实际的Univer API结构验证背景色
      const ruleObj = rule as { rule?: { style?: { bg?: { rgb?: string } } } }
      if (rule && ruleObj.rule?.style?.bg) {
        const bgColor = ruleObj.rule.style.bg.rgb
        log.debug(`背景色比较：期望 ${expectedColor}，实际 ${bgColor}`)

        // 支持多种颜色格式的比较，忽略大小写
        const normalizedExpected = expectedColor.toUpperCase()
        const normalizedActual = bgColor ? bgColor.toUpperCase() : ''

        // 检查是否匹配期望的颜色（支持多种格式）
        const isMatchingColor = this.isColorMatch(normalizedActual, normalizedExpected)

        if (isMatchingColor) {
          log.debug('背景色验证通过')
          return true
        } else {
          log.debug('背景色不匹配')
        }
      } else {
        log.debug('未找到背景色样式')
      }

      return false
    } catch (error) {
      log.error('检查规则背景色失败:', error)
      return false
    }
  }

  /**
   * 检查规则应用范围是否匹配
   */
  private checkRuleRange(rule: unknown, expectedRange: string): boolean {
    try {
      log.debug('检查规则范围:', { rule, expectedRange })

      // 检查模拟规则的范围（直接字符串比较）
      const ruleObj = rule as { range?: string }
      if (rule && ruleObj.range) {
        log.debug('模拟规则范围:', ruleObj.range)
        const rangeMatch = ruleObj.range === expectedRange
        log.debug(`范围匹配结果: ${ruleObj.range} === ${expectedRange} = ${rangeMatch}`)
        return rangeMatch
      }

      // 根据实际的Univer API结构验证范围
      const ruleWithRanges = rule as { ranges?: unknown[] }
      if (rule && ruleWithRanges.ranges && ruleWithRanges.ranges.length > 0) {
        const range = ruleWithRanges.ranges[0] as {
          startRow?: number;
          startColumn?: number;
          endRow?: number;
          endColumn?: number;
        }
        log.debug('API规则范围:', range)

        // 解析期望范围
        const expectedRangeInfo = this.parseRange(expectedRange)
        if (!expectedRangeInfo) {
          log.debug(`无法解析期望范围: ${expectedRange}`)
          return false
        }

        // 验证范围是否匹配
        const rangeMatch = range.startRow === expectedRangeInfo.startRow &&
                          range.startColumn === expectedRangeInfo.startColumn &&
                          range.endRow === expectedRangeInfo.endRow &&
                          range.endColumn === expectedRangeInfo.endColumn

        if (rangeMatch) {
          log.debug(`API范围验证通过: 行${range.startRow}-${range.endRow}，列${range.startColumn}-${range.endColumn}`)
          return true
        } else {
          log.debug(`API范围不匹配，期望：${expectedRange}(行${expectedRangeInfo.startRow}-${expectedRangeInfo.endRow}，列${expectedRangeInfo.startColumn}-${expectedRangeInfo.endColumn})，实际：行${range.startRow}-${range.endRow}，列${range.startColumn}-${range.endColumn}`)
        }
      } else {
        log.debug('未找到范围信息')
      }

      return false
    } catch (error) {
      log.error('检查规则范围失败:', error)
      return false
    }
  }

  /**
   * 检查颜色是否匹配
   */
  private isColorMatch(actualColor: string, expectedColor: string): boolean {
    try {
      // 标准化颜色值
      const normalizeColor = (color: string) => {
        if (!color) return ''
        return color.toLowerCase().replace(/#/g, '')
      }

      const normalizedActual = normalizeColor(actualColor)
      const normalizedExpected = normalizeColor(expectedColor)

      // 支持多种红色格式
      const redColors = ['ff0000', 'f05252', 'red', 'ff5252', 'e53e3e']

      if (redColors.includes(normalizedExpected)) {
        return redColors.includes(normalizedActual)
      }

      return normalizedActual === normalizedExpected
    } catch (error) {
      log.error('颜色匹配检查失败:', error)
      return false
    }
  }

  /**
   * 验证范围边框设置
   */
  private async validateRangeBorder(
    worksheet: any,
    rangeAddress: string,
    expectedBorder: string,
    expectedBorderColor?: string
  ): Promise<{
    isValid: boolean;
    actualBorder: string;
    actualBorderColor?: string;
  }> {
    try {
      log.debug('验证范围边框:', { rangeAddress, expectedBorder, expectedBorderColor })

      const range = worksheet.getRange(rangeAddress)
      if (!range) {
        return {
          isValid: false,
          actualBorder: '无法获取范围'
        }
      }

      // 解析范围，获取所有单元格
      const cells = this.getCellsInRange(rangeAddress)
      log.debug('范围内的单元格:', cells)

      if (cells.length === 0) {
        return {
          isValid: false,
          actualBorder: '范围解析失败'
        }
      }

      // 根据期望的边框类型验证
      switch (expectedBorder) {
        case 'outline':
          return await this.validateOutlineBorder(worksheet, rangeAddress, cells, expectedBorderColor)
        case 'all':
          return await this.validateAllBorders(worksheet, rangeAddress, cells, expectedBorderColor)
        case 'thick':
          return await this.validateThickBorder(worksheet, rangeAddress, cells, expectedBorderColor)
        default:
          return {
            isValid: false,
            actualBorder: `不支持的边框类型: ${expectedBorder}`
          }
      }
    } catch (error) {
      log.error('验证范围边框失败:', error)
      return {
        isValid: false,
        actualBorder: '验证失败'
      }
    }
  }

  /**
   * 验证外边框
   */
  private async validateOutlineBorder(
    worksheet: any,
    rangeAddress: string,
    cells: string[],
    expectedBorderColor?: string
  ): Promise<{ isValid: boolean; actualBorder: string; actualBorderColor?: string }> {
    try {
      log.debug('验证外边框:', { rangeAddress, cells, expectedBorderColor })

      // 解析范围
      const parsed = this.parseRange(rangeAddress)
      const { startRow, endRow, startColumn, endColumn } = parsed

      // 检查边界单元格是否有边框
      const borderChecks = []

      // 检查顶部边框
      for (let col = startColumn; col <= endColumn; col++) {
        const cellAddress = this.getCellAddress(startRow, col)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'top', expectedBorderColor)
        borderChecks.push({ cell: cellAddress, position: 'top', hasBorder })
      }

      // 检查底部边框
      for (let col = startColumn; col <= endColumn; col++) {
        const cellAddress = this.getCellAddress(endRow, col)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'bottom', expectedBorderColor)
        borderChecks.push({ cell: cellAddress, position: 'bottom', hasBorder })
      }

      // 检查左边框
      for (let row = startRow; row <= endRow; row++) {
        const cellAddress = this.getCellAddress(row, startColumn)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'left', expectedBorderColor)
        borderChecks.push({ cell: cellAddress, position: 'left', hasBorder })
      }

      // 检查右边框
      for (let row = startRow; row <= endRow; row++) {
        const cellAddress = this.getCellAddress(row, endColumn)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'right', expectedBorderColor)
        borderChecks.push({ cell: cellAddress, position: 'right', hasBorder })
      }

      const allBordersPresent = borderChecks.every(check => check.hasBorder)

      log.debug('外边框检查结果:', { borderChecks, allBordersPresent })

      return {
        isValid: allBordersPresent,
        actualBorder: allBordersPresent ? 'outline' : '部分或无外边框'
      }
    } catch (error) {
      log.error('验证外边框失败:', error)
      return {
        isValid: false,
        actualBorder: '验证失败'
      }
    }
  }

  /**
   * 验证所有边框
   */
  private async validateAllBorders(
    worksheet: any,
    rangeAddress: string,
    cells: string[],
    expectedBorderColor?: string
  ): Promise<{ isValid: boolean; actualBorder: string; actualBorderColor?: string }> {
    try {
      log.debug('验证所有边框:', { rangeAddress, cells, expectedBorderColor })

      // 检查每个单元格的所有边框
      const borderChecks = []

      for (const cellAddress of cells) {
        const topBorder = await this.checkCellBorder(worksheet, cellAddress, 'top', expectedBorderColor)
        const bottomBorder = await this.checkCellBorder(worksheet, cellAddress, 'bottom', expectedBorderColor)
        const leftBorder = await this.checkCellBorder(worksheet, cellAddress, 'left', expectedBorderColor)
        const rightBorder = await this.checkCellBorder(worksheet, cellAddress, 'right', expectedBorderColor)

        borderChecks.push({
          cell: cellAddress,
          top: topBorder,
          bottom: bottomBorder,
          left: leftBorder,
          right: rightBorder,
          allPresent: topBorder && bottomBorder && leftBorder && rightBorder
        })
      }

      const allCellsHaveAllBorders = borderChecks.every(check => check.allPresent)

      log.debug('所有边框检查结果:', { borderChecks, allCellsHaveAllBorders })

      return {
        isValid: allCellsHaveAllBorders,
        actualBorder: allCellsHaveAllBorders ? 'all' : '部分或无内边框'
      }
    } catch (error) {
      log.error('验证所有边框失败:', error)
      return {
        isValid: false,
        actualBorder: '验证失败'
      }
    }
  }

  /**
   * 验证粗边框
   */
  private async validateThickBorder(
    worksheet: any,
    rangeAddress: string,
    cells: string[],
    expectedBorderColor?: string
  ): Promise<{ isValid: boolean; actualBorder: string; actualBorderColor?: string }> {
    try {
      log.debug('验证粗边框:', { rangeAddress, cells, expectedBorderColor })

      // 解析范围
      const parsed = this.parseRange(rangeAddress)
      const { startRow, endRow, startColumn, endColumn } = parsed

      // 检查边界单元格是否有粗边框
      const borderChecks = []

      // 检查顶部粗边框
      for (let col = startColumn; col <= endColumn; col++) {
        const cellAddress = this.getCellAddress(startRow, col)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'top', expectedBorderColor, true)
        borderChecks.push({ cell: cellAddress, position: 'top', hasBorder })
      }

      // 检查底部粗边框
      for (let col = startColumn; col <= endColumn; col++) {
        const cellAddress = this.getCellAddress(endRow, col)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'bottom', expectedBorderColor, true)
        borderChecks.push({ cell: cellAddress, position: 'bottom', hasBorder })
      }

      // 检查左粗边框
      for (let row = startRow; row <= endRow; row++) {
        const cellAddress = this.getCellAddress(row, startColumn)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'left', expectedBorderColor, true)
        borderChecks.push({ cell: cellAddress, position: 'left', hasBorder })
      }

      // 检查右粗边框
      for (let row = startRow; row <= endRow; row++) {
        const cellAddress = this.getCellAddress(row, endColumn)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'right', expectedBorderColor, true)
        borderChecks.push({ cell: cellAddress, position: 'right', hasBorder })
      }

      const allBordersPresent = borderChecks.every(check => check.hasBorder)

      log.debug('粗边框检查结果:', { borderChecks, allBordersPresent })

      return {
        isValid: allBordersPresent,
        actualBorder: allBordersPresent ? 'thick' : '部分或无粗边框'
      }
    } catch (error) {
      log.error('验证粗边框失败:', error)
      return {
        isValid: false,
        actualBorder: '验证失败'
      }
    }
  }

  /**
   * 获取范围内的所有单元格地址
   */
  private getCellsInRange(rangeAddress: string): string[] {
    const cells: string[] = []

    try {
      const match = rangeAddress.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/)
      if (!match) {
        // 单个单元格
        return [rangeAddress]
      }

      const [, startCol, startRow, endCol, endRow] = match
      const startColIndex = this.getColumnIndex(startCol)
      const endColIndex = this.getColumnIndex(endCol)
      const startRowIndex = parseInt(startRow)
      const endRowIndex = parseInt(endRow)

      for (let row = startRowIndex; row <= endRowIndex; row++) {
        for (let col = startColIndex; col <= endColIndex; col++) {
          const colLetter = this.getColumnLetter(col)
          cells.push(`${colLetter}${row}`)
        }
      }
    } catch (error) {
      log.error('解析范围失败:', error)
    }

    return cells
  }



  /**
   * 解析范围地址
   */
  private parseRange(rangeAddress: string): {
    startRow: number;
    endRow: number;
    startColumn: number;
    endColumn: number;
  } {
    const match = rangeAddress.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/)
    if (!match) {
      // 单个单元格
      const singleMatch = rangeAddress.match(/([A-Z]+)(\d+)/)
      if (singleMatch) {
        const [, col, row] = singleMatch
        const colIndex = this.getColumnIndex(col)
        const rowIndex = parseInt(row)
        return {
          startRow: rowIndex,
          endRow: rowIndex,
          startColumn: colIndex,
          endColumn: colIndex
        }
      }
      throw new Error(`无法解析范围: ${rangeAddress}`)
    }

    const [, startCol, startRow, endCol, endRow] = match
    return {
      startRow: parseInt(startRow),
      endRow: parseInt(endRow),
      startColumn: this.getColumnIndex(startCol),
      endColumn: this.getColumnIndex(endCol)
    }
  }

  /**
   * 获取单元格地址
   */
  private getCellAddress(row: number, column: number): string {
    const colLetter = this.getColumnLetter(column)
    return `${colLetter}${row}`
  }

  /**
   * 检查单元格边框
   */
  private async checkCellBorder(
    worksheet: any,
    cellAddress: string,
    position: 'top' | 'bottom' | 'left' | 'right' | 'all',
    expectedColor?: string,
    isThick?: boolean
  ): Promise<boolean> {
    try {
      log.debug('检查单元格边框:', { cellAddress, position, expectedColor, isThick })

      const range = worksheet.getRange(cellAddress)
      if (!range) {
        log.debug('无法获取单元格范围:', cellAddress)
        return false
      }

      // 获取单元格数据和样式信息
      const cellData = range.getCellData()
      let style: any = {}

      // 根据Univer文档，样式可能是ID引用或直接的样式对象
      if (cellData?.s) {
        if (typeof cellData.s === 'string') {
          // 如果是字符串，说明是样式ID，需要从工作簿样式表中获取
          const workbook = this.univerAPI.getActiveWorkbook()
          let styles = {}

          try {
            // 使用save()方法获取工作簿数据，按照文档建议
            try {
              const workbookData = await (workbook as any).save()
              styles = workbookData?.styles || {}
              log.debug('使用save()获取样式表:', { totalStyles: Object.keys(styles).length })
            } catch (saveError) {
              log.debug('save()方法失败，尝试其他方法:', saveError)

              // 备用方法：尝试从Univer实例获取
              const univerInstance = (this.univerAPI as any)?._univerInstance
              if (univerInstance) {
                const currentWorkbook = univerInstance.getCurrentUniverSheetInstance()
                const workbookSnapshot = currentWorkbook?.save?.() || currentWorkbook?.getSnapshot?.()
                styles = workbookSnapshot?.styles || {}
              }
            }

            log.debug('获取到的样式表:', { totalStyles: Object.keys(styles).length, styleId: cellData.s })
          } catch (error) {
            log.debug('获取样式表失败:', error)
          }

          style = (styles as any)[cellData.s] || {}
          log.debug('从样式表获取样式:', { styleId: cellData.s, style, hasStyle: !!(styles as any)[cellData.s] })
        } else {
          // 如果是对象，直接使用
          style = cellData.s
          log.debug('直接使用样式对象:', style)
        }
      }

      // 根据Univer文档，边框信息存储在bd属性中
      const borderData = style.bd
      if (!borderData) {
        log.debug('单元格无边框数据:', cellAddress)
        return false
      }

      // 处理'all'位置的特殊情况
      if (position === 'all') {
        const topBorder = borderData.t
        const bottomBorder = borderData.b
        const leftBorder = borderData.l
        const rightBorder = borderData.r

        const hasAllBorders = !!(topBorder && topBorder.s && topBorder.s > 0 &&
                                bottomBorder && bottomBorder.s && bottomBorder.s > 0 &&
                                leftBorder && leftBorder.s && leftBorder.s > 0 &&
                                rightBorder && rightBorder.s && rightBorder.s > 0)

        log.debug(`单元格 ${cellAddress} 所有边框检查:`, {
          top: !!(topBorder && topBorder.s && topBorder.s > 0),
          bottom: !!(bottomBorder && bottomBorder.s && bottomBorder.s > 0),
          left: !!(leftBorder && leftBorder.s && leftBorder.s > 0),
          right: !!(rightBorder && rightBorder.s && rightBorder.s > 0),
          hasAllBorders
        })

        return hasAllBorders
      }

      // 根据位置获取对应的边框信息
      let borderInfo
      switch (position) {
        case 'top':
          borderInfo = borderData.t
          break
        case 'bottom':
          borderInfo = borderData.b
          break
        case 'left':
          borderInfo = borderData.l
          break
        case 'right':
          borderInfo = borderData.r
          break
      }

      if (!borderInfo) {
        log.debug(`单元格 ${cellAddress} 的 ${position} 边框不存在`)
        return false
      }

      log.debug('边框信息:', { cellAddress, position, borderInfo })

      // 检查边框样式 (s属性)
      const borderStyle = borderInfo.s
      if (borderStyle === undefined || borderStyle === null) {
        log.debug(`单元格 ${cellAddress} 的 ${position} 边框样式未设置`)
        return false
      }

      // 如果需要检查粗边框
      if (isThick) {
        // 根据Univer文档，边框样式值越大表示越粗
        // 通常粗边框的样式值会大于普通边框
        const isThickBorder = borderStyle >= 2 // 假设样式值2及以上为粗边框
        if (!isThickBorder) {
          log.debug(`单元格 ${cellAddress} 的 ${position} 边框不是粗边框，样式值: ${borderStyle}`)
          return false
        }
      } else {
        // 检查是否有边框（样式值大于0）
        if (borderStyle <= 0) {
          log.debug(`单元格 ${cellAddress} 的 ${position} 边框样式值无效: ${borderStyle}`)
          return false
        }
      }

      // 如果指定了期望的边框颜色，检查颜色
      if (expectedColor) {
        const borderColor = borderInfo.cl?.rgb
        if (!borderColor) {
          log.debug(`单元格 ${cellAddress} 的 ${position} 边框无颜色信息`)
          return false
        }

        // 颜色比较（标准化处理）
        const normalizeColor = (color: string) => {
          if (!color) return ''
          return color.toLowerCase().replace(/#/g, '')
        }

        const actualNormalized = normalizeColor(borderColor)
        const expectedNormalized = normalizeColor(expectedColor)

        if (actualNormalized !== expectedNormalized) {
          log.debug(`单元格 ${cellAddress} 的 ${position} 边框颜色不匹配:`, {
            expected: expectedColor,
            actual: borderColor,
            expectedNormalized,
            actualNormalized
          })
          return false
        }
      }

      log.debug(`单元格 ${cellAddress} 的 ${position} 边框验证通过`)
      return true

    } catch (error) {
      log.error(`检查单元格 ${cellAddress} 的 ${position} 边框失败:`, error)
      return false
    }
  }

}
