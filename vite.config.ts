import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [sveltekit()],
	build: {
		// 增加chunk大小警告限制
		chunkSizeWarningLimit: 1000
	},
	optimizeDeps: {
		// 预构建依赖
		include: [
			'@univerjs/core',
			'@univerjs/engine-render',
			'@univerjs/engine-formula',
			'@univerjs/sheets',
			'@univerjs/sheets-ui',
			'@univerjs/facade'
		]
	}
});
